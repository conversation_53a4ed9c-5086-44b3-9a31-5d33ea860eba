<route lang="json5">
{
  style: {
    navigationBarTitleText: '命理定制',
    navigationStyle: 'custom', // 隐藏原生导航栏，使用自定义导航栏
    disableScroll: true,
  },
}
</route>

<template>
  <view
    class="h-100vh w-100vw bg-#272224"
    :style="{
      backgroundImage: `url(${getImagesUrl('6859625ae4b00e5b2cf3618f.png')})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
    }"
  >
    <view
      class="overflow-hidden flex items-center justify-center"
      :style="{ paddingTop: safeAreaInsets?.top + 'px' }"
    >
      <view class="i-carbon-chevron-left c-#fff w-100rpx text-47rpx font-bold" @click="goBack()" />
      <view
        class="flex-1 center pr-100rpx font-500 text-32rpx c-#fff font-[PingFangSC,PingFang_SC]"
      ></view>
    </view>
    <view class="productDetailsButtonBodyClass">
      <view>
        <wd-button
          type="error"
          custom-class="custom-shadow"
          :round="false"
          @click="goTestInformation"
        >
          马上测试
        </wd-button>
      </view>
      <view class="mt-37rpx">
        <view>
          <wd-button
            type="error"
            custom-class="custom-shadowYq"
            :round="false"
            plain
            open-type="share"
          >
            邀好友测一测
          </wd-button>
        </view>
      </view>
      <view class="c-#fff center mt-50rpx">测算结果由AI生成,仅供参考</view>
    </view>
  </view>
  <MessageBoxHb
    ref="MessageBoxHbElement"
    :msg="msg"
    cancelButtonText="知道了"
    :confirmButton="false"
  />
</template>

<script lang="ts" setup>
import MessageBoxHb from '@/components/MessageBoxHb.vue'
import { getImagesUrl } from '@/utils/getImagesUrl'
import { getBannerList } from '@/utils/getBannerList'
import { userInfo, IUserInfoItem } from '@/service/myArchive/index'
import { aiChat, IAiChatItem, getTestResult } from '@/service/feedbackOnOpinions/index'
// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()
const msg = ref('')

// 默认分享图片
const defaultShareImage = ref('')

// 获取默认分享图
const fetchDefaultShareImage = async () => {
  try {
    const list = await getBannerList('14')
    if (Array.isArray(list) && list.length > 0) {
      defaultShareImage.value = list[0].picture || ''
    }
  } catch (e) {
    console.error('获取默认分享图失败', e)
  }
}

onMounted(() => {
  fetchDefaultShareImage()
})
// 返回上一级
function goBack() {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    // 如果有上一级页面，返回上一页
    uni.navigateBack({
      delta: 1,
    })
  } else {
    // 如果已经是首页或栈底，重定向到首页
    uni.reLaunch({
      url: '/pages/index/index',
    })
  }
}

// 分享
onShareAppMessage(() => {
  return {
    title: '命理定制',
    path: 'pages/testEightCharacters/index',
    imageUrl: defaultShareImage.value || '',
  }
})

// 跳转测试信息
async function goTestInformation() {
  // uni.showLoading({
  //   title: '正在测算中...',
  //   mask: true,
  // })
  // const isComplete = await getUserInfo()
  // if (isComplete) {
  //   getAiChat()
  // } else {
  //   uni.hideLoading()
  //   uni.navigateTo({ url: '/pages/testInformation/index' })
  // }
  uni.navigateTo({ url: '/pages/testInformation/index' })
}
// // 开始测试八字
// function getAiChat() {
//   const { data, code, run } = useRequest<IAiChatItem>(() => aiChat())
//   run().then(() => {
//     uni.hideLoading()
//     if (code.value === '0') {
//       getTestResult(data.value)
//       uni.navigateTo({ url: '/pages/startTesting/index' })
//     } else if (code.value === '1') {
//       msg.value = '当前使用人数过多，请稍后重试'
//       showMessageBox()
//     } else if (code.value === '500') {
//       msg.value = '当前已达测试次数限制，无法测试'
//       showMessageBox()
//     }
//   })
// }

// // 获取用户基础信息，全部字段齐全返回 true
// async function getUserInfo(): Promise<boolean> {
//   const { data, run } = useRequest<IUserInfoItem>(() => userInfo())

//   // 等待请求结束
//   await run()

//   // data.value 可能还是 undefined，先判空
//   const { gender, birthday, birthTime, birthArea } = data.value ?? {}

//   // 字段全部存在即视为信息完整
//   return !!(gender && birthday && birthTime && birthArea)
// }

// 引入消息框组件
const MessageBoxHbElement = ref<InstanceType<typeof MessageBoxHb> | null>(null)
// 显示消息框
function showMessageBox() {
  if (MessageBoxHbElement.value) {
    MessageBoxHbElement.value.open()
  }
}
</script>

<style lang="scss" scoped>
.productDetailsButtonBodyClass {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  height: 400rpx;
  padding: 0 60rpx;
}
//商品底部按钮结束位置

:deep() {
  .custom-shadow {
    width: 100%;
    height: 92rpx !important;
    font-size: 34rpx !important;
    font-weight: bold !important;
    color: #fff3e4 !important;
  }
  .custom-shadowYq {
    width: 100%;
    height: 92rpx !important;
    font-size: 34rpx !important;
    font-weight: bold !important;
    color: #d14343 !important;
  }
}
</style>
