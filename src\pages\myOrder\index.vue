<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的订单',
  },
}
</route>

<template>
  <view v-if="total">
    <view
      class="myOrderItemClass"
      v-for="item of myOrderIListData"
      :key="item.id"
      @click="goToOrderDetails(item)"
    >
      <view class="myOrderItemCellClass" v-if="item.list.length == 1">
        <view class="myOrderItemCellImageClass">
          <image :src="item.list[0].picture" class="b-rd-15rpx" mode="aspectFill" />
        </view>
        <view class="myOrderItemCellTitleClass">{{ item.list[0].name }}</view>
      </view>
      <view class="myOrderItemCellArrClass" v-else-if="item.list.length > 1">
        <wd-row>
          <wd-col :span="8" v-for="(imagesItem, index) of item.list" :key="index">
            <view class="myOrderItemCellImageClass">
              <image :src="imagesItem.picture" class="b-rd-15rpx" mode="aspectFill" />
            </view>
          </wd-col>
        </wd-row>
      </view>
      <view class="myOrderItemPriceClass">
        <text class="myOrderItemPriceSlClass">共 {{ item.total }} 件</text>
        <text class="myOrderItemPriceQClass">¥{{ item.amount }}</text>
      </view>
    </view>
    <wd-loadmore
      :state="state"
      @reload="loadmore"
      :loading-props="{ color: '#e73c3c', size: 20 }"
    />
  </view>
  <EmptyState v-else>
    <view class="lh-39rpx text-27rpx fw-bold font-[SourceHanSerifCN] c-#cac8c8 text-center">
      暂无订单
    </view>
  </EmptyState>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import EmptyState from '@/components/EmptyState.vue'
import {
  IOrderPageItem,
  orderPage,
  IOrderPageQuery,
  getActionData,
  IOrderGroup,
} from '@/service/myOrder/index'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { onReachBottom } from '@dcloudio/uni-app'
// 商品列表数据
const myOrderIListData = ref<IOrderGroup[]>([])
const pages = ref<number>(1)
const state = ref<LoadMoreState>('finished')
const total = ref(0)
// 跳转商品详情
function goToOrderDetails(item: IOrderGroup) {
  getActionData(item)
  uni.navigateTo({
    url: '/pages/orderDetails/index',
  })
}

// 页面加载时加载数据
onReachBottom(() => {
  if (myOrderIListData.value.length < total.value) {
    loadmore()
  } else {
    state.value = 'finished'
  }
})

// 加载更多数据
async function loadmore() {
  await getOrderPage()
  if (myOrderIListData.value.length >= total.value) {
    state.value = 'finished'
  } else {
    state.value = 'loading'
  }
}

// 获取订单信息
async function getOrderPage() {
  const parameter: IOrderPageQuery = { page: pages.value, size: 10 }
  const { data, run } = useRequest<IOrderPageItem>(() => orderPage(parameter))
  run().then(() => {
    pages.value++

    myOrderIListData.value = [...myOrderIListData.value, ...data.value.list]
    total.value = data.value.total
  })
}

onShow(() => {
  pages.value = 1
  myOrderIListData.value = []
  getOrderPage()
})
</script>

<style lang="scss" scoped>
.myOrderItemClass {
  padding: 20rpx;
  margin: 25rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .myOrderItemCellClass {
    display: flex;
    height: 200rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f3f3f3;
    .myOrderItemCellImageClass {
      width: 200rpx;
      height: 200rpx;
      background: #fff;
      border-radius: 15rpx;
    }
    .myOrderItemCellTitleClass {
      flex: 1;
      padding-left: 20rpx;
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 30rpx;
      color: #000000;
    }
  }
  .myOrderItemCellArrClass {
    border-bottom: 1rpx solid #f3f3f3;
    .myOrderItemCellImageClass {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 10px;
      background: #fff;
      border-radius: 15rpx;
    }
  }
  .myOrderItemPriceClass {
    margin-top: 20rpx;
    text-align: right;
    .myOrderItemPriceSlClass {
      padding-right: 10rpx;
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 37rpx;
      color: #000000;
      text-align: left;
    }
    .myOrderItemPriceQClass {
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 30rpx;
      color: #d14343;
      text-align: left;
    }
  }
}
</style>
