<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单详情',
  },
}
</route>

<template>
  <div class="orderDetailsListClass">
    <view class="myOrderItemCellClass" v-for="item of orderDetailsList.list" :key="item.id">
      <view class="myOrderItemCellImageClass">
        <image :src="item.picture" class="b-rd-15rpx" mode="aspectFill" />
      </view>
      <view class="myOrderItemCellTitleClass">
        <view class="myOrderItemCellTitleNameClass">{{ item.name }}</view>
        <view class="myOrderItemCellTitlePriceClass">
          <text class="myOrderItemCellTitlePriceInnerClass">¥{{ item.price }}</text>
          <text class="myOrderItemCellTitleNumberClass">X {{ item.num }}</text>
        </view>
      </view>
    </view>
    <view class="myOrderItemCellPieceClass">
      <text class="myOrderItemCellPieceInnerClass">共{{ orderDetailsList.total }}件</text>
      <text class="myOrderItemCellPiecesClass">¥{{ orderDetailsList.amount }}</text>
    </view>
  </div>
  <view class="orderDetailsClass">
    <view class="leftViewClass"></view>
    <view class="rightViewClass"></view>
    <view class="orderDetailsBodyClass">
      <view class="orderDetailsBodyLeftClass">订单编号</view>
      <view class="orderDetailsBodyRightClass">{{ orderDetailsList.id }}</view>
    </view>
    <view class="orderDetailsBodyClass">
      <view class="orderDetailsBodyLeftClass">创建时间</view>
      <view class="orderDetailsBodyRightClass">{{ orderDetailsList.createTime }}</view>
    </view>
    <view class="orderDetailsBodyClass">
      <view class="orderDetailsBodyLeftClass">客服</view>
      <view class="orderDetailsBodyRightClass">{{ orderDetailsList.createUser }}</view>
    </view>
    <view class="orderDetailsBodyClass">
      <view class="orderDetailsBodyLeftClass">配送方式</view>
      <view class="orderDetailsBodyRightClass">{{ orderDetailsList.logisticsName }}</view>
    </view>
    <view class="orderDetailsBodyClass">
      <view class="orderDetailsBodyLeftClass">快递单号</view>
      <view class="orderDetailsBodyRightClass">{{ orderDetailsList.expressCode }}</view>
    </view>
    <view class="orderDetailsBodyClass">
      <view class="orderDetailsBodyLeftClass">收货地址</view>
      <view class="orderDetailsBodyRightClass">{{ orderDetailsList.address || '' }}</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { dataList, IOrderGroup, getActionData } from '@/service/myOrder/index'
// 订单详情数据
const orderDetailsList = ref<IOrderGroup>(dataList)
// 关闭详情页时，清除缓存详情
onHide(() => {
  getActionData(null)
})
</script>

<style lang="scss" scoped>
.orderDetailsListClass {
  padding: 30rpx 30rpx 47rpx 30rpx;
  margin: 25rpx 25rpx 0 25rpx;
  background: #fff;
  border-bottom: 1rpx dashed #c0c0c0;
  border-radius: 15rpx 15rpx 0 0;
  .myOrderItemCellClass {
    display: flex;
    padding-bottom: 20rpx;
    .myOrderItemCellImageClass {
      width: 170rpx;
      height: 170rpx;
      background: #fff;
      border-radius: 15rpx;
    }
    .myOrderItemCellTitleClass {
      flex: 1;
      padding-left: 20rpx;
      .myOrderItemCellTitleNameClass {
        font-family: SourceHanSerifCN;
        font-size: 26rpx;
        font-style: normal;
        font-weight: bold;
        line-height: 30rpx;
        color: #000000;
        text-align: left;
      }
      .myOrderItemCellTitlePriceClass {
        margin-top: 24rpx;
        .myOrderItemCellTitlePriceInnerClass {
          float: left;
          font-family: SourceHanSerifCN;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 30rpx;
          color: #666666;
        }
        .myOrderItemCellTitleNumberClass {
          float: right;
          font-family: SourceHanSerifCN;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 30rpx;
          color: #666666;
        }
      }
    }
  }
  .myOrderItemCellPieceClass {
    text-align: right;
    .myOrderItemCellPieceInnerClass {
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 48rpx;
      color: #000000;
      text-align: left;
    }
    .myOrderItemCellPiecesClass {
      padding-left: 10rpx;
      font-family: SourceHanSerifCN;
      font-size: 34rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 48rpx;
      color: #000000;
      text-align: left;
    }
  }
}

.orderDetailsClass {
  position: relative;
  height: 400rpx;
  padding: 30rpx;
  margin: 0rpx 25rpx 0 25rpx;
  background: #fff;
  border-radius: 0 0 15rpx 15rpx;
  .leftViewClass {
    position: absolute;
    top: -36rpx;
    left: 0;
    width: 36rpx;
    height: 72rpx;
    background: #f8f8f8;
    border-radius: 0 36rpx 36rpx 0;
  }
  .rightViewClass {
    position: absolute;
    top: -36rpx;
    right: 0;
    width: 36rpx;
    height: 72rpx;
    background: #f8f8f8;
    border-radius: 36rpx 0 0 36rpx;
  }
  .orderDetailsBodyClass {
    display: flex;
    .orderDetailsBodyLeftClass {
      width: 200rpx;
      padding: 20rpx 0;
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 30rpx;
      color: #666666;
      text-align: left;
    }
    .orderDetailsBodyRightClass {
      flex: 1;
      padding: 20rpx 0;
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 30rpx;
      color: #1e2127;
      text-align: right;
    }
  }
}
</style>
