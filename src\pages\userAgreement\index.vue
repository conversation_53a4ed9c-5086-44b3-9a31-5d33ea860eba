<route lang="json5">
{
  style: {
    navigationBarTitleText: '用户协议',
  },
}
</route>

<template>
  <view class="p-20rpx">
    <mp-html :content="curbannerData?.content" />
  </view>
</template>

<script lang="ts" setup>
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import { commonProtocol, ICommonProtocolItem } from '@/service/index/index'
import { onShow, onUnload } from '@dcloudio/uni-app'
import { useLoginGuard } from '@/composables/useTheme'
const { checkLogin } = useLoginGuard()
defineOptions({
  name: 'userAgreement',
})

const curbannerData = ref<ICommonProtocolItem>(null)

// 获取切换标签数据
function getCommonProtocol() {
  const { data, run } = useRequest<ICommonProtocolItem>(() => commonProtocol('1'))
  run().then(() => {
    curbannerData.value = data.value
  })
}
onShow(() => {
  getCommonProtocol()
})
onUnload(() => {
  checkLogin()
})
</script>

<style lang="scss" scoped></style>
