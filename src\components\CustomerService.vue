<template>
  <wd-overlay :show="show" @click="show = false" :zIndex="999">
    <view class="wrapper">
      <view
        class="block"
        @click.stop=""
        :style="{
          backgroundImage: `url(${getImagesUrl('685961ace4b00e5b2cf3618e.png')})`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundSize: 'contain',
        }"
      >
        <view class="qrCodeBody">
          <image :src="QrUrl" mode="aspectFill" :show-menu-by-longpress="true" />
        </view>
        <view class="contentInformation">长按识别二维码，添加客服微信，获取更多定制详情～</view>
        <view class="closeButton" @click.stop="show = false">
          <view class="iconClass">
            <view class="i-carbon-close-large" />
          </view>
        </view>
      </view>
    </view>
  </wd-overlay>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { commonQr } from '@/service/index/index'
import { getImagesUrl } from '@/utils/getImagesUrl'
const show = ref(false)
defineOptions({ name: 'CustomerService' })
const QrUrl = ref('')
// 打开
function open() {
  const { data, run } = useRequest<string>(() => commonQr())
  run().then(() => {
    QrUrl.value = data.value
    show.value = true
  })
}

// 暴露 open 方法给父组件调用
defineExpose({ open })
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  position: relative;
  width: 680rpx;
  height: 970rpx;
  .qrCodeBody {
    width: 370rpx;
    height: 370rpx;
    padding: 15rpx;
    margin: 404rpx auto auto auto;
    background: #ffffff;
    border-radius: 20rpx;
  }
  .contentInformation {
    width: 506rpx;
    height: 86rpx;
    margin: 50rpx auto auto auto;
    font-family: SourceHanSerifCN;
    font-size: 31rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 43rpx;
    color: #9d3232;
    text-align: center;
  }
  .closeButton {
    position: absolute;
    bottom: -160rpx;
    left: 50%;
    width: 80rpx;
    height: 80rpx;
    background-color: #fcf4f3;
    border-radius: 40rpx;
    transform: translate(-50%, -50%);
    .iconClass {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
}
</style>
