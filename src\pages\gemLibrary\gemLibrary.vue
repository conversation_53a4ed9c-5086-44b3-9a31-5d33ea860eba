<route lang="json5">
{
  style: {
    navigationBarTitleText: '宝石库',
    navigationBarBackgroundColor: '#F8F8F8',
    backgroundColor: '#F8F8F8',
  },
}
</route>

<template>
  <view>
    <TabSelector
      custom-class="pt-20rpx mb-22rpx"
      :tabs="tabs"
      @change="changeTab"
      :max="4"
    ></TabSelector>
    <view
      v-if="curTab == '6' && !list.length"
      class="w700rpx min-h1025rpx overflow-auto bg-white rounded-20rpx mx-25rpx pt-36rpx center flex-col"
    >
      <image class="w212rpx h206rpx" src="../../static/gemLibrary/Fill.svg" />
      <text class="mt-57rpx h46rpx leading-46rpx text-32rpx font-500">
        完成命理测试，了解我的喜用
      </text>
      <view class="mt-40rpx">
        <wd-button
          custom-class="w270rpx h85rpx rounded-15rpx!"
          :round="false"
          @click="goTestEightCharacters"
        >
          马上测一测
        </wd-button>
      </view>
    </view>
    <view class="mx-25rpx" v-else>
      <view class="grid grid-cols-5 gap-15rpx" v-if="curTab !== '6'">
        <wd-button
          v-for="name in fiveElements"
          :key="name.id"
          size="small"
          :type="curCategory == name.id ? 'primary' : 'info'"
          custom-class="w100% h52rpx  rounded-20rpx! font-500"
          @click="switchCategory(name.id)"
        >
          {{ name.typeName }}
        </wd-button>
      </view>
      <view
        class="w100% min-h1025rpx overflow-auto bg-white rounded-20rpx mt-20rpx pt-36rpx px-20rpx center flex-col box-border"
      >
        <scroll-view class="w100%">
          <block v-if="list.length > 0">
            <view
              v-for="item in list"
              :key="item.id"
              class="w100% grid box-border gap-35rpx mb-56rpx"
              style="grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr))"
            >
              <view
                v-if="curTab === '0'"
                class="w140rpx h48rpx text-34rpx leading-34rpx font-bold col-span-full mr-auto relative flex justify-center"
              >
                <text>{{ item.name }}&nbsp;/&nbsp;</text>
                <image class="w34rpx h34rpx" :src="getMateriaNameIcon(item.name)" />
                <image
                  class="w100% absolute left-0 bottom-0 h34rpx"
                  referrerpolicy="no-referrer"
                  src="../../static//gemLibrary/beadNameFooterImage.png"
                />
              </view>
              <view
                class="w100% h48rpx text-34rpx leading-34rpx font-bold col-span-full flex items-center"
                v-else
              >
                <text class="pl-20rpx" style="border-left: 1px solid #000">
                  {{ item.name }}
                </text>
              </view>
              <block v-if="item.list.length > 0">
                <view
                  v-for="im in item.list"
                  class="flex flex-col gap-20rpx justify-center items-center"
                  :key="im.id"
                  @click="goDetail(im.id)"
                >
                  <image class="w100rpx h100rpx" :src="im.picture" round />
                  <view class="text-22rpx whitespace-nowrap box center">{{ im.name }}</view>
                </view>
              </block>
              <EmptyState v-else height="50rpx" noImage>
                <view
                  class="lh-39rpx text-27rpx fw-bold font-[SourceHanSerifCN] c-#cac8c8 text-center"
                >
                  暂无数据
                </view>
              </EmptyState>
            </view>
          </block>
          <EmptyState v-else>
            <view class="lh-39rpx text-27rpx fw-bold font-[SourceHanSerifCN] c-#cac8c8 text-center">
              暂无数据
            </view>
          </EmptyState>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import TabSelector from '@/components/TabSelector.vue'
import EmptyState from '@/components/EmptyState.vue'
import {
  gemEnjoyColor,
  IGemEnjoyColorItem,
  IGemTypeListItem,
  gemTypeList,
  gemList,
} from '@/service/gemLibrary'
import { useLoginGuard } from '@/composables/useTheme'

const { checkLogin } = useLoginGuard()

const list = ref<any[]>([])

const fiveElements = ref<any[]>([])

const curCategory = ref('') // 当前选中的类别

// 去测试八字
function goTestEightCharacters() {
  if (!checkLogin()) return
  uni.navigateTo({
    url: `/pages/testEightCharacters/index`,
  })
}

// 切换标签数据
const tabs: any = [
  { id: '6', name: '我的喜用' },
  { id: '0', name: '按五行' },
  { id: '1', name: '按色系' },
  { id: '2', name: '按材质' },
  { id: '3', name: '按珠型' },
  // { id: '4', name: '按隔片' },
  { id: '5', name: '按配饰' },
]

const curTab = ref<string>('6')
// 切换tab
function changeTab(item: { id: string; name: string }, index: number) {
  const { id } = item
  curTab.value = id
  curCategory.value = ''
  getFilterFun()
}
// 筛选出相应的方法
function getFilterFun() {
  if (curTab.value === '6') {
    getGemEnjoyColor()
  } else {
    getGemTypeList()
    getGemList()
  }
}

// 获取我的喜用
function getGemEnjoyColor() {
  const { data, run } = useRequest<IGemEnjoyColorItem[]>(() => gemEnjoyColor())
  run().then(() => {
    list.value = data.value
  })
}
// 获取宝石标签
function getGemTypeList() {
  const { data, run } = useRequest<IGemTypeListItem[]>(() => gemTypeList(curTab.value))
  run().then(() => {
    fiveElements.value = data.value
  })
}

// 获取我的喜用
function getGemList() {
  const { data, run } = useRequest<IGemEnjoyColorItem[]>(() => gemList({ type: curTab.value }))
  run().then(() => {
    list.value = curCategory.value
      ? data.value.filter((e) => e.id === curCategory.value)
      : data.value
  })
}

const getMateriaNameIcon = (name: string) => {
  switch (name) {
    case '木':
      return '../../static/gemLibrary/mu.png'
    case '火':
      return '../../static/gemLibrary/huo.png'
    case '土':
      return '../../static/gemLibrary/tu.png'
    case '金':
      return '../../static/gemLibrary/jin.png'
    case '水':
      return '../../static/gemLibrary/shui.png'
    default:
      return ''
  }
}

const switchCategory = (category: string) => {
  curCategory.value = curCategory.value === category ? '' : category
  getGemList()
}

onMounted(() => {
  getFilterFun()
})

// 跳转详情页面
function goDetail(id: string) {
  if (!checkLogin()) return
  uni.navigateTo({ url: `/pages/gemLibrary/detail?id=${id}` })
}
</script>

<style lang="scss" scoped>
.box {
  position: relative;
  min-width: 100rpx;
  height: 40rpx;
  padding: 10rpx 10rpx;
  margin: auto;
  background: radial-gradient(
      var(--border_radius) at var(--border_radius) var(--border_radius),
      transparent calc(97% - var(--border_width)),
      var(--color) calc(100% - var(--border_width)) 98%,
      transparent
    ),
    linear-gradient(var(--color), var(--color)), linear-gradient(var(--color), var(--color)),
    linear-gradient(var(--color), var(--color)), linear-gradient(var(--color), var(--color));
  background-repeat: repeat, no-repeat, no-repeat, no-repeat, no-repeat;
  background-position:
    calc(-1 * var(--border_radius)) calc(-1 * var(--border_radius)),
    calc(var(--border_radius) - 1px) 0,
    calc(var(--border_radius) - 1px) 100%,
    // 两条横边
    0 calc(var(--border_radius) - 1px),
    100% calc(var(--border_radius) - 1px); // 两条纵边
  background-size:
    100% 100%,
    calc(100% - calc(var(--border_radius) * 2 - 2px)) var(--border_width),
    calc(100% - calc(var(--border_radius) * 2 - 2px)) var(--border_width),
    var(--border_width) calc(100% - calc(var(--border_radius) * 2 - 2px)),
    var(--border_width) calc(100% - calc(var(--border_radius) * 2 - 2px));
  --color: #f9c6c9;
  --border_radius: 10rpx;
  --border_width: 1px;
}
</style>
