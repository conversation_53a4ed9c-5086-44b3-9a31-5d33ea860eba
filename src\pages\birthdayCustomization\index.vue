<route lang="json5">
{
  style: {
    navigationBarTitleText: '生辰定制',
  },
}
</route>

<template>
  <view
    class="h-385rpx bg-#fff"
    :style="{
      backgroundImage: imageUrl ? `url(${imageUrl})` : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center',
      backgroundSize: 'contain',
    }"
  ></view>
  <!-- 切换标签开始位置 -->
  <TabSelector :tabs="tabs" @change="changeTab" :max="4"></TabSelector>
  <!-- 切换标签结束位置 -->
  <view class="long-image-content">
    <image
      v-for="item in longImageSlices"
      :key="item.id"
      :src="item.picture"
      mode="widthFix"
      class="long-image-slice"
    />
  </view>
  <view class="productDetailsButtonBodyPlaceholderClass" v-if="tabsValue === '6'"></view>
  <view class="productDetailsButtonBodyClass" v-if="tabsValue === '6'">
    <view class="productDetailsButtonBodyInnerClass" @click="showMessageBox">
      <wd-button custom-class="custom">立即定制</wd-button>
    </view>
  </view>
  <CustomerService ref="CustomerServiceElement" />
</template>

<script lang="ts" setup>
import { onShow } from '@dcloudio/uni-app'
import TabSelector from '@/components/TabSelector.vue'
import { getBannerList } from '@/utils/getBannerList'
import { IBannerItem } from '@/service/index/index'
import CustomerService from '@/components/CustomerService.vue'

const imageUrl = computed(() => birthdayCustomization.value[0]?.picture || '')

const birthdayCustomization = ref<IBannerItem[]>([])

// 切换标签数据
const tabs: any = [
  { id: '6', name: '定制流程' },
  { id: '7', name: '案例展示' },
  { id: '8', name: '手作现场' },
  { id: '9', name: '大师介绍' },
]

const tabsValue = ref('6')
const longImageSlices = ref([])

// tab切换事件
async function changeTab(item: any, index: number) {
  const { id } = item
  tabsValue.value = id
  longImageSlices.value = await getBannerList(id)
}

onShow(async () => {
  birthdayCustomization.value = await getBannerList('5')
  longImageSlices.value = await getBannerList('6')
})

const CustomerServiceElement = ref(null)

// 显示消息框
function showMessageBox() {
  if (CustomerServiceElement.value) {
    CustomerServiceElement.value.open()
  }
}
</script>

<style lang="scss" scoped>
.long-image-content {
  display: flex;
  flex-direction: column;
  margin: 30rpx;
}
.long-image-slice {
  display: block;
  width: 100%;
}

//商品底部按钮开始位置
.productDetailsButtonBodyPlaceholderClass {
  height: 188rpx;
}
.productDetailsButtonBodyClass {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  height: 188rpx;
  background: #fff;
  .productDetailsButtonBodyInnerClass {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    :deep() {
      //按钮样式
      .custom {
        width: 90%;
        height: 81rpx;
        background: #e73c3c;
      }
      //按钮圆角
      .wd-button.is-round {
        border-radius: 13rpx;
      }
      //按钮文字样式
      .wd-button__text {
        font-size: 32rpx;
        font-weight: bold;
        color: #fff9f3;
      }
    }
  }
  //商品底部按钮结束位置
}
</style>
