<route lang="json5">
{
  style: {
    navigationBarTitleText: '饰品定制',
  },
}
</route>

<template>
  <view v-if="!loadingIndex">
    <!-- 海报开始位置 -->
    <view class="diyShowPosterClass">
      <image :src="diyShowPosterIamge[0]?.picture" mode="widthFix" />
      <div class="diyShowPosterClassNumber">
        <text class="fw-700">{{ peopleNumber }}</text>
        <text class="text-28rpx">人</text>
      </div>
    </view>
    <!-- 海报结束位置 -->
    <!-- 定制按钮开始位置 -->
    <view class="m-15rpx">
      <wd-row>
        <wd-col :span="12">
          <view class="diyButtonClass">
            <wd-button custom-class="custom" @click="goToBirthCustomize">
              <image
                class="diyButtonIconClassL pb-8rpx"
                src="../../static/diy/scdzImage.png"
                mode="widthFix"
              />
              <text class="pl-20rpx text-32rpx">生辰定制</text>
            </wd-button>
          </view>
        </wd-col>
        <wd-col :span="12">
          <view class="diyButtonClass">
            <wd-button custom-class="custom" @click="goFreeCustomize">
              <image
                class="diyButtonIconClassR"
                src="../../static/diy/zydzImage.png"
                mode="widthFix"
              />
              <text class="pl-20rpx text-32rpx">自由定制</text>
            </wd-button>
          </view>
        </wd-col>
      </wd-row>
    </view>
    <!-- 定制按钮结束位置 -->

    <!-- 切换标签开始位置 -->
    <TabSelector :tabs="tabs" @change="changeTab" :max="4"></TabSelector>
    <!-- 切换标签结束位置 -->

    <!-- 商品展示开始位置 -->
    <view class="m-9rpx">
      <wd-row>
        <wd-col :span="12" v-for="item in goodsListData" :key="item.id">
          <view
            class="goodsItemClass"
            hover-class="goodsItemHover"
            :hover-stay-time="150"
            @click="goToDiyProductDetails(item)"
          >
            <image :src="item.picture" class="goodsImagesClass" mode="aspectFill" />
            <view class="goodsDescribeClass">{{ item.name }}</view>
            <view class="goodsTextClass">
              <wd-row>
                <wd-col :span="8">
                  <view class="goodsPriceClass">
                    ¥
                    <text class="text-32rpx">{{ item.price }}</text>
                  </view>
                </wd-col>
                <wd-col :span="16">
                  <view class="flex justify-end flex-wrap mt-4rpx">
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('健康')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #027741">健康</view>
                    </view>
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('财富')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #e96f00">财富</view>
                    </view>
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('爱情')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #fd466f">爱情</view>
                    </view>
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('事业')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #6b4023">事业</view>
                    </view>
                    <view class="goodsTypeClass ml-5rpx" v-if="item.labels.includes('平安')">
                      <image
                        src="../../static/home/<USER>"
                        class="goodsTypeImagesClass"
                        mode="aspectFill"
                      />
                      <view class="goodsTypeTextClass" style="color: #e32117">平安</view>
                    </view>
                  </view>
                </wd-col>
              </wd-row>
            </view>
          </view>
        </wd-col>
      </wd-row>
      <wd-loadmore
        :state="state"
        @reload="loadmore"
        :loading-props="{ color: '#e73c3c', size: 20 }"
      />
    </view>
  </view>
  <!-- 商品展示结束位置 -->
  <MessageBoxHb
    ref="MessageBoxHbElement"
    msg="您可选择下方定制商品，或选择自由定制"
    cancelButtonText="返回"
    confirmButtonText="开始定制"
    @confirm="confirm"
    @cancel="cancel"
  />
</template>

<script lang="ts" setup>
import TabSelector from '@/components/TabSelector.vue'
import { onReachBottom, onShow } from '@dcloudio/uni-app'
import MessageBoxHb from '@/components/MessageBoxHb.vue'
import { getBannerList } from '@/utils/getBannerList'
import { useUserStore } from '@/store'
import { IBannerItem } from '@/service/index/index'
import { useLoginGuard } from '@/composables/useTheme'
import {
  ICustomTypeListItem,
  ITabsListItem,
  customTypeList,
  ICustomPageQuery,
  ICustomPageItem,
  customPage,
  commonTotalUser,
} from '@/service/diy/index'

import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
const { checkLogin } = useLoginGuard()
// 切换标签数据
const tabs = ref<ITabsListItem[]>([])
const userStore = useUserStore()
const peopleNumber = ref(0)

// 定制海报图片
const diyShowPosterIamge = ref<IBannerItem[]>([])

// 获取切换标签数据
async function getCustomTypeList() {
  const { data, run } = useRequest<ICustomTypeListItem[]>(() => customTypeList())
  run().then(() => {
    tabs.value = data.value
    if (data.value.length > 0) {
      typeId.value = data.value[0].id
      getCustomPage(typeId.value)
    }
  })
}
// 获取切换标签数据
async function getCommonTotalUser() {
  const { data, run } = useRequest<number>(() => commonTotalUser())
  run().then(() => {
    peopleNumber.value = data.value
  })
}

const loadingIndex = ref(true)
// 商品列表
const goodsListData = ref([])
const state = ref<LoadMoreState>('finished')
const total = ref(0)
const typeId = ref(null)
// 页数
const pages = ref<number>(1)

// tab切换事件
function changeTab(item: ITabsListItem, index: number) {
  const { id } = item
  typeId.value = id
  pages.value = 1
  total.value = 0
  goodsListData.value = []
  state.value = 'finished'
  getCustomPage(id)
}

// 页面加载时加载数据
onReachBottom(() => {
  if (goodsListData.value.length < total.value) {
    loadmore()
  } else {
    state.value = 'finished'
  }
})

// 加载更多数据
async function loadmore() {
  await getCustomPage(typeId.value)
  if (goodsListData.value.length >= total.value) {
    state.value = 'finished'
  } else {
    state.value = 'loading'
  }
}
// 获取商品详情
function getCustomPage(id: string) {
  const parameter: ICustomPageQuery = {
    page: pages.value,
    typeId: id,
    size: 10,
  }
  const { data, run } = useRequest<ICustomPageItem>(() => customPage(parameter))
  run().then(() => {
    pages.value++
    const list = data.value.list.map((e) => {
      return {
        ...e,
        labels: e.labels.length <= 2 ? e.labels : e.labels.slice(0, 2),
      }
    })
    goodsListData.value = [...goodsListData.value, ...list]
    total.value = data.value.total
  })
}
// 跳转自由定制页面
function goFreeCustomize() {
  if (!checkLogin()) return
  uni.navigateTo({ url: '/pages/diy/modules/freeCustomize' })
}
// 跳转到商品详情页面
function goToDiyProductDetails(item: any) {
  if (!checkLogin()) return
  uni.navigateTo({
    url: `/pages/diyProductDetails/index?id=${item.id}`,
  })
}
// 跳转生辰定制页面
function goToBirthCustomize() {
  if (!checkLogin()) return
  uni.navigateTo({ url: '/pages/birthdayCustomization/index' })
}

// 引入消息框组件
const MessageBoxHbElement = ref<InstanceType<typeof MessageBoxHb> | null>(null)
// 显示消息框
function showMessageBox() {
  if (MessageBoxHbElement.value) {
    MessageBoxHbElement.value.open()
  }
}
// 确认定制
function confirm() {
  userStore.setUserInfo({ customized: true })
}
// 取消定制
function cancel() {
  uni.reLaunch({ url: '/pages/index/index' })
}

onShow(async () => {
  if (!userStore.userInfo.customized) {
    uni.showLoading({
      title: '加载中',
    })
    pages.value = 1
    total.value = 0
    goodsListData.value = []
    state.value = 'finished'
    diyShowPosterIamge.value = await getBannerList('4')
    getCustomTypeList()
    loadingIndex.value = false
    uni.hideLoading()
    // 页面加载时显示消息框
    nextTick(() => {
      if (!loadingIndex.value) {
        showMessageBox()
      }
    })
  }
})
onShow(() => {
  // 获取总用户数
  getCommonTotalUser()
})

onBeforeMount(async () => {
  if (userStore.userInfo.customized) {
    uni.showLoading({
      title: '加载中',
    })
    state.value = 'finished'
    diyShowPosterIamge.value = await getBannerList('4')
    getCustomTypeList()
    loadingIndex.value = false
    uni.hideLoading()
  }
})
</script>

<style lang="scss" scoped>
//海报css开始位置
.diyShowPosterClass {
  position: relative;
  height: 598rpx;
  .diyShowPosterClassNumber {
    position: absolute;
    bottom: 90rpx;
    left: 43rpx;
    width: 270rpx;
    font-size: 48rpx;
    color: #e92020;
    text-align: center;
  }
}
//海报css结束位置
//定制按钮css开始位置
.diyButtonClass {
  height: 90rpx;
  margin: 15rpx;
  .diyButtonIconClassL {
    width: 54rpx;
    height: 44rpx;
  }
  .diyButtonIconClassR {
    width: 54rpx;
    height: 44rpx;
  }
  :deep() {
    //按钮样式
    .custom {
      width: 100%;
      height: 90rpx !important;
      background: #e73c3c;
    }
    //按钮圆角
    .wd-button.is-round {
      border-radius: 13rpx;
    }
    //按钮文字样式
    .wd-button__text {
      font-size: 32rpx;
      font-weight: bold;
      color: #fff9f3;
    }
  }
}
//定制按钮css结束位置

//商品展示位css开始位置
.goodsItemClass {
  height: 510rpx;
  margin: 6rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .goodsImagesClass {
    height: 354rpx;
    background: #f8eded;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
  }
  .goodsDescribeClass {
    width: 311rpx;
    height: 64rpx;
    margin: 20rpx auto 0 auto;
    font-size: 25rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 32rpx;
    color: #000000;
    text-align: left;
  }
  .goodsTextClass {
    width: 311rpx;
    margin: 15rpx auto 0 auto;
    .goodsPriceClass {
      font-family: SourceHanSerifCN;
      font-size: 29rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 41rpx;
      color: #fc3b3b;
      text-align: left;
    }
    .goodsTypeClass {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 90rpx;
      height: 35rpx;
      background: rgba(117, 117, 117, 0.07);
      border-radius: 18rpx;
      .goodsTypeImagesClass {
        width: 23rpx;
        height: 18rpx;
      }
      .goodsTypeTextClass {
        font-family: SourceHanSerifCN;
        font-size: 22rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 22rpx;
      }
    }
  }
}
.goodsItemHover {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1); // 添加阴影效果
  transform: scale(0.97); // 点击时缩小一点
}
//商品展示位css结束位置
</style>
