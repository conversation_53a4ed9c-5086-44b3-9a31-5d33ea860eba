<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的档案',
  },
}
</route>

<template>
  <view class="myArchiveBodyClass">
    <wd-cell-group custom-class="mx-10rpx" v-if="isTrue">
      <wd-cell custom-class="py-10rpx" title="昵称" title-width="150rpx" center>
        <wd-input
          type="text"
          custom-class="customInput"
          v-model="userArchive.nickName"
          placeholder=""
          no-border
        />
      </wd-cell>
      <wd-cell custom-class="py-20rpx" title="性别" center>
        <wd-radio-group
          v-model="userArchive.gender"
          inline
          shape="dot"
          @change="
            (e) => {
              userArchive.genderName = e.value == '1' ? '男' : '女'
            }
          "
        >
          <wd-radio value="1">男</wd-radio>
          <wd-radio value="2">女</wd-radio>
        </wd-radio-group>
      </wd-cell>
      <wd-datetime-picker
        custom-class="py-20rpx"
        align-right
        type="date"
        v-model="userArchive.birthday"
        :minDate="minDate"
        :maxDate="Date.now()"
        label="出生日期"
        title="请选择年月日"
        confirm-button-text="确定"
        :default-value="defaultValue"
        :formatter="formatter"
        @confirm="handleConfirmBirthday"
      />
      <wd-datetime-picker
        type="time"
        custom-class="py-20rpx"
        align-right
        title="请选择出生时间-时、分"
        v-model="userArchive.birthTime"
        confirm-button-text="确定"
        :formatter="formatter"
        label="出生时间"
      />
      <view class="txtClass">若时间不详，可将12:00作为参考</view>
      <wd-col-picker
        custom-class="py-20rpx"
        label="出生地点"
        title="请选择出生地点"
        v-model="userArchive.birthAreaArray"
        :columns="areaAddress"
        :column-change="columnChange"
        @confirm="handleConfirm"
        align-right
        auto-complete
      ></wd-col-picker>
      <wd-picker
        custom-class="py-20rpx"
        :columns="columnSize"
        label="手围"
        title="请选择手围"
        confirm-button-text="确定"
        align-right
        v-model="userArchive.handSize"
        @confirm="handleConfirmSize"
      />
      <wd-select-picker
        custom-class="py-20rpx"
        :columns="columnsColor"
        label="喜好颜色"
        align-right
        value-key="id"
        title="请选择喜好颜色"
        label-key="typeName"
        v-model="userArchive.color"
        @confirm="getColorName"
      ></wd-select-picker>
      <wd-col-picker
        custom-class="py-20rpx"
        label="收货地址"
        title="请选择收货地址"
        v-model="userArchive.addressArray"
        :columns="areaAddressSh"
        :column-change="columnChange"
        @confirm="handleConfirmSh"
        align-right
        center
      ></wd-col-picker>
      <wd-cell custom-class="py-10rpx" title="详细地址" title-width="150rpx" center>
        <wd-input
          type="text"
          custom-class="customInput"
          v-model="userArchive.addressDetail"
          placeholder=""
          no-border
        />
      </wd-cell>
      <view class="pb-20rpx pt-20rpx">
        <wd-row :gutter="10">
          <wd-col :span="9">
            <wd-button type="info" custom-class="custom1" @click="isTrue = false" :round="false">
              取消
            </wd-button>
          </wd-col>
          <wd-col :span="15">
            <wd-button custom-class="custom" @click="onSave" :round="false">保存</wd-button>
          </wd-col>
        </wd-row>
      </view>
    </wd-cell-group>
    <wd-cell-group custom-class="mx-10rpx" v-else>
      <wd-cell
        custom-class="py-20rpx"
        title="昵称"
        title-width="150rpx"
        :value="userArchive.nickName"
      />
      <wd-cell
        custom-class="py-20rpx"
        title="性别"
        title-width="150rpx"
        :value="userArchive.genderName"
      />
      <wd-cell
        custom-class="py-20rpx"
        title="出生日期"
        title-width="150rpx"
        :value="userArchive.birthdayDisplay"
      />
      <wd-cell
        custom-class="py-20rpx"
        title="出生时间"
        title-width="150rpx"
        :value="userArchive.birthTime"
      />
      <wd-cell
        custom-class="py-20rpx"
        title="出生地点"
        title-width="150rpx"
        :value="userArchive.birthArea"
      />
      <wd-cell
        custom-class="py-20rpx"
        title="手围"
        title-width="150rpx"
        :value="userArchive.handSizeDisplay"
      />
      <wd-cell
        custom-class="py-20rpx"
        title="喜好颜色"
        title-width="150rpx"
        :value="userArchive.colorDisplay"
      />
      <wd-cell
        custom-class="py-20rpx"
        title="五行喜用"
        title-width="150rpx"
        :value="userArchive.enjoyUse"
      />
      <wd-cell
        custom-class="py-20rpx"
        title="收货地址"
        title-width="150rpx"
        :value="addressDetails"
      />
      <view class="pb-20rpx pt-20rpx">
        <wd-button custom-class="custom" @click="onModify" :round="false">编辑</wd-button>
      </view>
    </wd-cell-group>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useColPickerData } from '@/hooks/useColPickerData'
import dayjs from 'dayjs'
import {
  userInfo,
  IUserInfoItem,
  IGemTypeListItem,
  gemTypeList,
  userUpdate,
  IUserInfoQuery,
} from '@/service/myArchive/index'
import { onShow } from '@dcloudio/uni-app'
import { useUserStore } from '@/store'
const userStore = useUserStore()
// 定义最小日期
const minDate = dayjs(new Date('1900-01-01')).valueOf()
const defaultValue = ref<number>(Date.now())
const formatter = (type, value) => {
  switch (type) {
    case 'year':
      return value + '年'
    case 'month':
      return value + '月'
    case 'date':
      return value + '日'
    case 'hour':
      return value + '时'
    case 'minute':
      return value + '分'
    default:
      return value
  }
}
// 定义用户档案信息
const userArchive = ref<IUserInfoQuery>({
  nickName: '',
  gender: '',
  genderName: '',
  birthday: '',
  birthdayDisplay: '',
  birthTime: '',
  handSize: '',
  handSizeDisplay: '',
  color: [],
  colorDisplay: '',
  enjoyUse: '', // 五行喜用
  birthAreaArray: [], // 出生地址编码
  birthArea: '', // 出生地址名称
  addressArray: [], // 收获地址编码
  address: '', // 收获地址名称
  addressDetail: '', // 手动输入详细地址
})
const addressDetails = computed(() => {
  return userArchive.value.address + ' ' + userArchive.value.addressDetail
})

// 定义是否编辑状态
const isTrue = ref(false)
// 手围数据
const columnSize = ref([
  {
    value: '1',
    label: '13-14cm',
  },
  {
    value: '2',
    label: '14-15cm',
  },
  {
    value: '3',
    label: '15-16cm',
  },
  {
    value: '4',
    label: '16-17cm',
  },
  {
    value: '5',
    label: '17-18cm',
  },
  {
    value: '6',
    label: '18-19cm',
  },
  {
    value: '7',
    label: '19-20cm',
  },
  {
    value: '8',
    label: '20cm以上',
  },
])
// 颜色数据
const columnsColor = ref<IGemTypeListItem[]>([])
// 获取地区数据方法
const { colPickerData, findChildrenByCode } = useColPickerData()
// 出生地点初始数据
const areaAddress = ref<any[]>([
  colPickerData.map((item) => {
    return {
      value: item.value,
      label: item.text,
    }
  }),
])
// 收货地址初始数据
const areaAddressSh = ref<any[]>([
  colPickerData.map((item) => {
    return {
      value: item.value,
      label: item.text,
    }
  }),
])
// 地址根据地址id初始化数据
function getAddress(item: string[]) {
  const data = [
    colPickerData.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
    findChildrenByCode(colPickerData, item[0])!.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
    findChildrenByCode(colPickerData, item[1])!.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
  ]
  return data
}

function getAddress2(item: string[]) {
  const data = [
    colPickerData.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
  ]
  return data
}
// 地址数据变更事件
const columnChange = ({ selectedItem, resolve, finish }) => {
  const areaData = findChildrenByCode(colPickerData, selectedItem.value)
  if (areaData && areaData.length) {
    resolve(
      areaData.map((item) => {
        return {
          value: item.value,
          label: item.text,
        }
      }),
    )
  } else {
    finish()
  }
}
// 编辑资料(已选择地址初始化)
function onModify() {
  isTrue.value = true
  if (userArchive.value.birthAreaArray.length !== 0) {
    areaAddress.value = getAddress(userArchive.value.birthAreaArray)
  } else {
    areaAddress.value = getAddress2(userArchive.value.birthAreaArray)
  }
  if (userArchive.value.addressArray.length !== 0) {
    areaAddressSh.value = getAddress(userArchive.value.addressArray)
  } else {
    areaAddressSh.value = getAddress2(userArchive.value.addressArray)
  }
}
// 获取显示出生时间
function handleConfirmBirthday({ value }) {
  userArchive.value.birthdayDisplay = dayjs(value).format('YYYY-MM-DD')
}
// 获取显示出生地点
function handleConfirm({ selectedItems }: { selectedItems: { value: string; label: string }[] }) {
  userArchive.value.birthArea = selectedItems.map((item) => item.label).join(' ')
}
// 获取手围显示
function handleConfirmSize({ selectedItems }) {
  userArchive.value.handSizeDisplay = selectedItems.label
}

// 获取收货地址显示
function handleConfirmSh({ selectedItems }: { selectedItems: { value: string; label: string }[] }) {
  userArchive.value.address = selectedItems.map((item) => item.label).join(' ')
}
// 获取颜色名称
function getColorName({ value, selectedItems }) {
  userArchive.value.colorDisplay = selectedItems
    .map((item) => {
      return item.typeName
    })
    .join(', ')
}

// 保存用户档案信息
function onSave() {
  const prams: IUserInfoQuery = {
    ...userArchive.value,
    color: userArchive.value.colorDisplay,
    birthday:
      userArchive.value.birthday || typeof userArchive.value.birthday === 'number'
        ? Number(dayjs(userArchive.value.birthday).format('YYYYMMDD'))
        : '',
    // 出生时间，格式 HHmm
    birthTime: userArchive.value.birthTime
      ? Number(String(userArchive.value.birthTime).replace(':', ''))
      : '',
    birthAreaArray: userArchive.value.birthAreaArray.length
      ? userArchive.value.birthAreaArray.join('-')
      : '', // 出生地址编码
    addressArray: userArchive.value.addressArray.length
      ? userArchive.value.addressArray.join('-')
      : '', // 收获地址编码
  }
  const { data, run } = useRequest<IUserInfoItem>(() => userUpdate(prams))
  run().then(() => {
    uni.showToast({ icon: 'none', title: '信息保存成功' })
    const userInfo = { nickName: userArchive.value.nickName, gender: userArchive.value.gender }
    userStore.setUserInfo(userInfo)
    isTrue.value = false
  })
}

// 获取用户基础信息
function getUserInfo() {
  const { data, run } = useRequest<IUserInfoItem>(() => userInfo())
  run().then(() => {
    if (!data.value) return
    const {
      nickName,
      gender,
      birthday,
      birthTime,
      handSize,
      color,
      birthAreaArray,
      birthArea,
      addressArray,
      address,
      addressDetail,
      enjoyUse,
    } = data.value
    const item = {
      nickName,
      gender: gender ? gender.toString() : null,
      birthday: birthday ? dayjs(String(birthday), 'YYYYMMDD').startOf('day').valueOf() : '',
      birthTime: birthTime ? formatToHHmm(birthTime) : birthday && !birthTime ? '00:00' : '',
      handSize: handSize || '',
      colorDisplay: color || '',
      enjoyUse: enjoyUse || '', // 五行喜用
      color: color
        ? columnsColor.value.filter((item) => color.includes(item.typeName)).map((item) => item.id)
        : [],
      birthAreaArray: birthAreaArray ? birthAreaArray.split('-') : [],
      birthArea,
      addressArray: addressArray ? addressArray.split('-') : [],
      address: address || '',
      addressDetail: addressDetail || '',
      genderName: gender ? (gender == '1' ? '男' : '女') : null,
      birthdayDisplay: birthday ? dayjs(String(birthday)).format('YYYY-MM-DD') : null,
      handSizeDisplay: handSize
        ? columnSize.value.filter((e) => e.value === handSize)[0].label
        : '',
    }
    userArchive.value = item
  })
}
// 补齐时间
function formatToHHmm(num: number | string): string {
  const padded = num.toString().padStart(4, '0')
  const hours = padded.slice(0, 2)
  const minutes = padded.slice(2, 4)
  return `${hours}:${minutes}`
}

// 获取色系字典
async function getGemTypeList() {
  const { data, run } = useRequest<IGemTypeListItem[]>(() => gemTypeList('1'))
  run().then(() => {
    columnsColor.value = data.value
  })
}

onShow(async () => {
  await getGemTypeList()
  getUserInfo()
})
</script>

<style lang="scss" scoped>
//档案信息bodyCSS开始位置
.myArchiveBodyClass {
  margin: 25rpx;
  background: #ffffff;
  border-radius: 20rpx;
  :deep(.customInput) {
    height: 76rpx;
    background: #f6f6f6;
    border-radius: 13rpx;
    opacity: 0.75;
  }
}
//档案信息bodyCSS结束位置

//时间提示文字css开始位置
.txtClass {
  padding-right: 36rpx;
  font-family: SourceHanSerifCN;
  font-size: 27rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 39rpx;
  color: #666666;
  text-align: right;
}
//时间提示文字css结束位置

//穿透样式修改Css开始位置
:deep(.wd-input__inner) {
  height: 76rpx !important;
  padding: 0 20rpx !important;
  text-align: right !important;
}

:deep(.wd-cell__title) {
  font-family: SourceHanSerifCN;
  font-size: 30rpx !important;
  font-weight: bold;
  color: #1e2127;
}

:deep(.wd-picker__label) {
  font-family: SourceHanSerifCN;
  font-size: 30rpx !important;
  font-weight: bold;
  color: #1e2127;
}

:deep(.wd-select-picker__label) {
  font-family: SourceHanSerifCN;
  font-size: 30rpx !important;
  font-weight: bold;
  color: #1e2127;
}

:deep(.wd-col-picker__label) {
  font-family: SourceHanSerifCN;
  font-size: 30rpx !important;
  font-weight: bold;
  color: #1e2127;
}

:deep(.wd-picker-view-column__item--active) {
  color: #fc3b3b;
}

:deep(.wd-col-picker__selected-line) {
  background: #fc3b3b !important;
}

:deep() {
  //按钮样式
  .custom {
    width: 100%;
    height: 80rpx;
    background: #e73c3c;
  }
  .custom1 {
    width: 90%;
    height: 80rpx;
  }
  .wd-button.is-medium.is-round {
    min-width: 200rpx !important;
  }
  .wd-picker-view-column__item {
    white-space: pre !important;
  }
}
//穿透样式修改Css结束位置
</style>
