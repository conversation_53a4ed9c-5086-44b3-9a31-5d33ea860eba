<template>
  <wd-overlay :show="show" @click="close" :zIndex="9999">
    <view class="wrapper">
      <view
        class="block"
        :style="{
          backgroundImage: `url(${getImagesUrl('685948f7e4b00e5b2cf36186.png')})`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundSize: 'contain',
        }"
        @click.stop=""
      >
        <view class="blockTitle">{{ props.title }}</view>
        <view class="blockContent">{{ props.msg }}</view>
        <view class="blockContentButton">
          <view class="blockContentButtonInner">
            <view class="blockContentButtonInnerLeft" v-if="props.cancelButton" @click="cancel">
              <view class="blockContentButtonInnerLeftInner">{{ props.cancelButtonText }}</view>
            </view>
            <view class="blockContentButtonInnerRight" v-if="props.confirmButton" @click="confirm">
              <view class="blockContentButtonInnerRightInner">{{ props.confirmButtonText }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </wd-overlay>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getImagesUrl } from '@/utils/getImagesUrl'
const show = ref(false)
defineOptions({ name: 'MessageBoxHb' })
const emit = defineEmits(['confirm', 'cancel'])

// prop基本属性
const props = defineProps({
  msg: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: '提示',
  },
  confirmButtonText: {
    type: String,
    default: '确定',
  },
  cancelButtonText: {
    type: String,
    default: '取消',
  },
  cancelButton: {
    type: Boolean,
    default: true,
  },
  confirmButton: {
    type: Boolean,
    default: true,
  },
})
// 关闭
function close() {
  show.value = false
}
// 打开
function open() {
  show.value = true
}
// 确认按钮点击事件
function confirm() {
  emit('confirm')
  close()
}
// 取消按钮点击事件
function cancel() {
  emit('cancel')
  close()
}
// 暴露 open 方法给父组件调用
defineExpose({ open })
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  position: relative;
  width: 690rpx;
  height: 390rpx;
  border-radius: 20rpx;
  .blockTitle {
    padding-left: 14rpx;
    margin-top: 54rpx;
    font-family: SourceHanSerifCN;
    font-size: 32rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 34rpx;
    color: #a92525;
    text-align: center;
  }
  .blockContent {
    padding: 0 14rpx;
    margin-top: 62rpx;
    font-family: SourceHanSerifCN;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 32rpx;
    color: #000000;
    text-align: center;
  }
  .blockContentButton {
    position: absolute;
    bottom: 60rpx;
    left: 0;
    width: 100%;
    text-align: center;
    .blockContentButtonInner {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 60rpx;
      .blockContentButtonInnerLeft {
        flex: 1;
        .blockContentButtonInnerLeftInner {
          width: 270rpx;
          height: 80rpx;
          margin: 0 auto;
          font-family: SourceHanSerifCN;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 80rpx;
          color: #000000;
          border: 1rpx solid #aaaaaa;
          border-radius: 13rpx;
        }
      }
      .blockContentButtonInnerLeftInner:active {
        background-color: rgba(0, 0, 0, 0.1); // 半透明点击态
      }
      .blockContentButtonInnerRight {
        flex: 1;
        .blockContentButtonInnerRightInner {
          width: 270rpx;
          height: 80rpx;
          margin: 0 auto;
          font-family: SourceHanSerifCN;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 80rpx;
          color: #ffffff;
          background: #e73c3c;
          border-radius: 13rpx;
        }
        .blockContentButtonInnerRightInner:active {
          background-color: rgba(231, 60, 60, 0.8);
        }
      }
    }
  }
}
</style>
