<template>
  <view class="flex-1 center empty-state-root" :style="{ minHeight: computedHeight }">
    <view class="w250rpx" :class="props.noImage ? '' : 'h300rpx'">
      <image
        v-if="!noImage"
        :src="getImagesUrl('68621d79e4b00e5b2b88f3fa.png')"
        class="h250rpx mb-10rpx"
        mode="aspectFill"
      />
      <slot></slot>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getImagesUrl } from '@/utils/getImagesUrl'
import { ref, computed, onMounted, getCurrentInstance, nextTick } from 'vue'

defineOptions({ name: 'EmptyState' })

const props = defineProps<{ height?: string | number; noImage?: boolean }>()

const remainingHeight = ref(0)
const instance = getCurrentInstance()

const computedHeight = computed(() => {
  if (props.height !== undefined) {
    return typeof props.height === 'number' ? props.height + 'px' : props.height
  }
  return remainingHeight.value + 'px'
})

onMounted(() => {
  if (props.height !== undefined) return

  const systemInfo = uni.getSystemInfoSync()
  nextTick(() => {
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('.empty-state-root')
      .boundingClientRect((rect) => {
        if (rect && !Array.isArray(rect)) {
          remainingHeight.value = systemInfo.windowHeight - rect.top - 20
        } else {
          remainingHeight.value = systemInfo.windowHeight
        }
      })
      .exec()
  })
})
</script>

<style lang="scss" scoped></style>
