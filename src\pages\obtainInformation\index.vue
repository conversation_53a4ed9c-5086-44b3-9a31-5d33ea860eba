<route lang="json5">
{
  style: {
    navigationBarTitleText: '头像和昵称',
  },
}
</route>

<template>
  <template>
    <view class="login-wrapper">
      <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
        <image class="avatar" :src="formData.avatar || defaultAvatar"></image>
        <text class="loginLogoText" v-if="isShow">点击授权头像</text>
      </button>

      <view class="nameContent">
        <view class="title">昵称</view>
        <input
          type="nickname"
          v-model="formData.nickName"
          placeholder="请输入昵称"
          class="weui-input"
        />
      </view>

      <view class="loginBtn">
        <wd-button custom-class="custom" :round="false" form-type="submit" @click="saveUserInfo">
          保存并返回
        </wd-button>
      </view>
    </view>
  </template>
</template>

<script lang="ts">
import { ref, watch } from 'vue'
import { useUserStore } from '@/store'
import { onShow } from '@dcloudio/uni-app'
import { userInfo, IUserInfoItem, IUserInfoQuery, userUpdate } from '@/service/myArchive/index'
import { uploadFile2 } from '@/hooks/useUpload'

export default {
  setup() {
    const defaultAvatar =
      'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
    const userStore = useUserStore()
    const formData = ref({
      nickName: '',
      avatar: '',
    })

    const isShow = ref(true)

    watch(
      () => formData.value.avatar,
      (val) => {
        if (val) {
          isShow.value = false
        }
      },
      { immediate: true },
    )

    async function onChooseAvatar(e: any) {
      const imgRes = await uploadFile2<any>(e.detail.avatarUrl)
      const imageUrl = imgRes.data.url
      formData.value.avatar = imageUrl
    }

    function saveUserInfo() {
      const { nickName, avatar } = formData.value
      if (!nickName || !avatar) {
        uni.showToast({
          title: '请填写昵称并选择头像',
          icon: 'none',
        })
        return
      }

      // 示例：保存至本地
      userStore.setUserInfo(formData.value)

      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })

      const prams: IUserInfoQuery = {
        ...formData.value,
      }
      const { data, run } = useRequest<IUserInfoItem>(() => userUpdate(prams))
      run().then(() => {
        uni.showToast({
          title: '保存成功',
          icon: 'success',
        })
        userStore.setUserInfo(formData.value)
        setTimeout(() => {
          uni.navigateBack()
        }, 1000)
      })
      // 也可以跳转页面，或 emit 回父页面
    }

    // 获取用户基础信息
    function getUserInfo() {
      const { data, run } = useRequest<IUserInfoItem>(() => userInfo())
      run().then(() => {
        if (!data.value) return
        const { nickName, avatar } = data.value
        const item = {
          nickName,
          avatar: avatar || '',
        }
        formData.value = item
      })
    }
    onShow(() => {
      getUserInfo()
    })
    return {
      formData,
      onChooseAvatar,
      saveUserInfo,
      isShow,
      defaultAvatar,
    }
  },
}
</script>

<style scoped lang="scss">
.login-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
}

.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;

  .avatar {
    width: 120rpx;
    height: 120rpx;
    background-color: #f2f2f2;
    border-radius: 50%;
  }

  .loginLogoText {
    margin-top: 20rpx;
    font-size: 24rpx;
    color: #999;
  }
}

.nameContent {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 30rpx 0;
  font-size: 28rpx;
  border-bottom: 1px solid #eee;

  .title {
    width: 80rpx;
    margin-right: 20rpx;
  }

  .weui-input {
    flex: 1;
  }
}

.loginBtn {
  width: 80%;
  margin-top: 60rpx;
}
:deep() {
  //按钮样式
  .custom {
    width: 100%;
    height: 80rpx;
    background: #e73c3c;
  }
  .custom1 {
    width: 90%;
    height: 80rpx;
  }
  .wd-button.is-medium.is-round {
    min-width: 200rpx !important;
  }
}
</style>
