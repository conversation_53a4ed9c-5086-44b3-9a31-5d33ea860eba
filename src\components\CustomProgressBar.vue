<template>
  <!-- 自定义遮罩层 -->
  <view v-if="show" class="custom-overlay" @click="close">
    <view class="wrapper" @click.stop>
      <view class="block">
        <!-- 🎯 文案 -->
        <view class="blockTitle">{{ titleText }}</view>
        <!-- 🎯 进度条 -->
        <view class="progress-bar">
          <view class="progress-bar-fill" :style="{ width: percent + '%' }"></view>
        </view>
        <view class="progress-text">{{ percent.toFixed(2) }}%</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, watch, onBeforeUnmount } from 'vue'

defineOptions({ name: 'CustomProgressBar' })

const show = ref(false)
const percent = ref(0)
let timer: number | undefined
const titleText = ref('正在测算中，请稍后...')

// 打开方法（供父组件调用）
function open() {
  console.log('open')
  show.value = true
  percent.value = 0

  if (timer) clearInterval(timer)
  timer = setInterval(() => {
    if (percent.value < 99) {
      const step = Math.floor(Math.random() * 5) + 1 // 1 ~ 5
      percent.value = Math.min(percent.value + step, 99)
    }
  }, 1000)
}

// 关闭方法
function close() {
  show.value = false
  if (timer) {
    clearInterval(timer)
    timer = undefined
  }
}

// 监听外部设置为 100 时自动关闭
watch(percent, (val) => {
  if (val >= 100) {
    titleText.value = '测算完成，正在前往...'
    setTimeout(() => {
      close()
    }, 1000)
  }
})

// 暴露给父组件
defineExpose({ open, percent })
onBeforeUnmount(() => {
  if (timer) clearInterval(timer)
})
</script>

<style lang="scss" scoped>
/* 遮罩层 */
.custom-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 690rpx;
  height: 390rpx;
  background-color: #fff;
  border-radius: 20rpx;
}

.blockTitle {
  margin-bottom: 20rpx;
  font-family: SourceHanSerifCN;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 34rpx;
  color: #e73c3c;
}

// 进度条
.progress-bar {
  width: 400rpx;
  height: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  background-color: #f3f3f3;
  border-radius: 10rpx;
}

.progress-bar-fill {
  height: 100%;
  background-color: #e73c3c;
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: #222;
}
</style>
