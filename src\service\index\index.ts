import { http } from '@/utils/http'
// 轮播图返回类型
export interface IBannerItem {
  formatType: number
  title: string
  ordered: number
  picture: string
  content: string
}
// 吉品标签类型
export interface ILuckyTypeListItem {
  id: string
  name: string
  ordered: number
}
// 标签类型
export interface ITabsListItem {
  id: string
  name: string
  ordered: number
}

// 获取开运吉品商品类型
export interface ILuckyPageQuery {
  sort?: Array<string>
  page?: number
  size?: number
  typeId?: string
}

export interface ILuckyPageListItem {
  id: string
  name: string
  labels: string[]
  luckyTypeId: string
  ordered: number
  picture: string
  price: number
}
// 获取开运吉品商品类型
export interface ILuckyPageItem {
  list: Array<ILuckyPageListItem>
  total: number
}

// 宝石项
export interface GemItem {
  id: string
  name: string
  picture: string
}

// 获取开运吉品商品详情类型
export interface ILuckyDetailItem {
  id: string
  name: string
  picture: string
  price: number
  url: string
  ordered: number
  labels: string[]
  luckyTypeId: string
  gemstones: string
  gemList: GemItem[]
  content: string
}

export interface ICommonProtocolItem {
  protocolFileName: string
  protocolFile: string
  protocolDesc: string
  content: string
}

/** GET 请求 根据类型查询banner
获取轮播图数据 1:首页顶部 2:首页介绍 3:命理定制 4:定制页顶部 5:生辰定制顶部 6:生辰定制-定制流程 7:生辰定制-案例展示 8:生辰定制-手作现场 9:生辰定制-大师介绍n */
export const bannerList = (type: string) => {
  return http.get<IBannerItem[]>(`/banner/list/${type}`)
}

/** GET 请求 获取开运吉品标签 */
export const luckyTypeList = () => {
  return http.get<ILuckyTypeListItem[]>('/lucky/typeList')
}

/** GET 请求 获取开运吉品商品 */
export const luckyPage = (data: ILuckyPageQuery) => {
  return http.get<ILuckyPageItem>('/lucky/page', { ...data })
}

/** GET 请求 获取客服二维码 */
export const commonQr = () => {
  return http.get<string>('/common/qr')
}

/** GET 请求 退出登陆 */
export const wxUserLogout = () => {
  return http.get<boolean>('/wx/user/logout')
}

/** GET 请求 获取开☁️吉品商品详情 */
export const luckyDetail = (id: string) => {
  return http.get<ILuckyDetailItem>(`/lucky/detail/${id}`)
}

/** GET 请求 获取协议 */
export const commonProtocol = (id: string) => {
  return http.get<ICommonProtocolItem>(`/common/protocol`, { type: id })
}

/** GET 请求 获取订单是否展示 */
export const orderShow = () => {
  return http.get<boolean>(`/common/orderShow`)
}
