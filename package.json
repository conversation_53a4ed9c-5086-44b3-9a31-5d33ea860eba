{"name": "uniquebead", "type": "commonjs", "version": "2.6.2", "description": "unibest - 最好的 uniapp 开发模板", "author": {"name": "feige996", "zhName": "菲鸽", "email": "<EMAIL>", "github": "https://github.com/feige996", "gitee": "https://gitee.com/feige996"}, "license": "MIT", "repository": "https://github.com/feige996/unibest", "repository-gitee": "https://gitee.com/feige996/unibest", "repository-deprecated": "https://github.com/codercup/unibest", "bugs": {"url": "https://github.com/feige996/unibest/issues"}, "homepage": "https://feige996.github.io/unibest/", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"preinstall": "npx only-allow pnpm", "uvm": "npx @dcloudio/uvm@latest", "uvm-rm": "node ./scripts/postupgrade.js", "postuvm": "echo upgrade uni-app success!", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev": "uni", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp": "uni -p mp-weixin", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp": "uni build -p mp-weixin", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "prepare": "git init && husky install", "type-check": "vue-tsc --noEmit", "cz": "czg", "openapi-ts-request": "openapi-ts"}, "lint-staged": {"**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "**/*.{vue,js,ts,jsx,tsx}": ["eslint --cache --fix"], "**/*.{vue,css,scss,html}": ["stylelint --fix"]}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020920240930001", "@dcloudio/uni-app-harmony": "3.0.0-4020920240930001", "@dcloudio/uni-app-plus": "3.0.0-4020920240930001", "@dcloudio/uni-components": "3.0.0-4020920240930001", "@dcloudio/uni-h5": "3.0.0-4020920240930001", "@dcloudio/uni-mp-alipay": "3.0.0-4020920240930001", "@dcloudio/uni-mp-baidu": "3.0.0-4020920240930001", "@dcloudio/uni-mp-jd": "3.0.0-4020920240930001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4020920240930001", "@dcloudio/uni-mp-lark": "3.0.0-4020920240930001", "@dcloudio/uni-mp-qq": "3.0.0-4020920240930001", "@dcloudio/uni-mp-toutiao": "3.0.0-4020920240930001", "@dcloudio/uni-mp-weixin": "3.0.0-4020920240930001", "@dcloudio/uni-mp-xhs": "3.0.0-4020920240930001", "@dcloudio/uni-quickapp-webview": "3.0.0-4020920240930001", "@tanstack/vue-query": "^5.62.16", "@vant/area-data": "^2.0.0", "abortcontroller-polyfill": "^1.7.8", "dayjs": "1.11.10", "mp-html": "^2.5.1", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "qs": "6.5.3", "vue": "3.4.21", "wot-design-uni": "^1.4.0", "z-paging": "^2.8.4"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-4020920240930001", "@dcloudio/uni-cli-shared": "3.0.0-4020920240930001", "@dcloudio/uni-stacktracey": "3.0.0-4020920240930001", "@dcloudio/vite-plugin-uni": "3.0.0-4020920240930001", "@esbuild/darwin-arm64": "0.20.2", "@esbuild/darwin-x64": "0.20.2", "@iconify-json/carbon": "^1.2.4", "@rollup/rollup-darwin-x64": "^4.28.0", "@types/node": "^20.17.9", "@types/wechat-miniprogram": "^3.4.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@uni-helper/uni-types": "1.0.0-alpha.3", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.7", "@uni-helper/vite-plugin-uni-pages": "0.2.20", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@uni-ku/root": "^1.3.0", "@unocss/preset-legacy-compat": "^0.59.4", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.20", "commitlint": "^18.6.1", "czg": "^1.9.4", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "husky": "^8.0.3", "lint-staged": "^15.2.10", "openapi-ts-request": "^1.1.2", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.12.0", "sass": "1.77.8", "stylelint": "^16.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-prettier": "^5.0.2", "terser": "^5.36.0", "typescript": "^5.7.2", "unocss": "^0.58.9", "unocss-applet": "^0.7.8", "unplugin-auto-import": "^0.17.8", "vite": "5.2.8", "vite-plugin-restart": "^0.4.2", "vue-tsc": "^1.8.27"}, "packageManager": "pnpm@10.12.3+sha512.467df2c586056165580ad6dfb54ceaad94c5a30f80893ebdec5a44c5aa73c205ae4a5bb9d5ed6bb84ea7c249ece786642bbb49d06a307df218d03da41c317417"}