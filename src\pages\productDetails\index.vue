<route lang="json5">
{
  style: {
    navigationBarTitleText: '商品详情',
  },
}
</route>

<template>
  <view class="productDetailsImagePalyClass">
    <image :src="productDetailsData?.picture" mode="aspectFill" />
  </view>
  <view class="productDetailsContentClass">
    <view class="goodsTextClass">
      <wd-row>
        <wd-col :span="4">
          <view class="goodsPriceClass">
            ¥
            <text class="text-40rpx">{{ productDetailsData?.price }}</text>
          </view>
        </wd-col>
        <wd-col :span="20">
          <view class="flex justify-end flex-wrap mt-4rpx">
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('健康')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #027741">健康</view>
            </view>
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('财富')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #e96f00">财富</view>
            </view>
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('爱情')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #fd466f">爱情</view>
            </view>
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('事业')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #6b4023">事业</view>
            </view>
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('平安')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #e32117">平安</view>
            </view>
          </view>
        </wd-col>
      </wd-row>
    </view>
    <view class="productDetailsTitleClass">{{ productDetailsData?.name }}</view>
    <view class="productDetailsEnsureClass">
      <image :src="getImagesUrl('68596e9fe4b00e5b2cf36194.png')" mode="aspectFill" />
    </view>
    <view class="productDetailsClassificationClass">宝石：{{ productDetailsData?.gemstones }}</view>
  </view>
  <view class="productDetailsTextClass">
    <view class="productDetailsTextTitleClass">详细介绍</view>
    <view class="productDetailsTextContentClass">
      <mp-html :content="productDetailsData?.content" />
    </view>
  </view>
  <view class="productDetailsButtonBodyPlaceholderClass"></view>
  <view class="productDetailsButtonBodyClass">
    <view class="productDetailsButtonBodyInnerClass">
      <button class="productDetailsButtonBodyInnerLeftClass" open-type="share">
        <view class="productDetailsButtonBodyInnerLeftShareClass">
          <image src="../../static/images/shareIcon.png" mode="aspectFill" />
          <view class="productDetailsButtonBodyInnerLeftShareTextClass">分享</view>
        </view>
      </button>
      <view class="productDetailsButtonBodyInnerRightClass">
        <wd-button custom-class="custom" @click="showMessageBox">立即购买</wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { ILuckyDetailItem, luckyDetail, GemItem } from '@/service/index/index'
import { getImagesUrl } from '@/utils/getImagesUrl'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import { getBannerList } from '@/utils/getBannerList'

defineOptions({
  name: 'productDetails',
})
const productDetailsData = ref<ILuckyDetailItem>(null)

const detailId = ref('')

// 默认分享图片
const defaultShareImage = ref('')

// 获取默认分享图（banner 类型 12 第一张）
const fetchDefaultShareImage = async () => {
  try {
    const list = await getBannerList('12')
    if (Array.isArray(list) && list.length > 0) {
      defaultShareImage.value = list[0].picture || ''
    }
  } catch (e) {
    console.error('获取默认分享图失败', e)
  }
}
onLoad((options) => {
  const { id } = options
  detailId.value = id
  getLuckyDetail(id)
  // 预取默认分享图
  fetchDefaultShareImage()
})
// 立即购买 - 跳转有赞商品链接
function showMessageBox() {
  if (!productDetailsData.value.url) {
    return uni.showToast({ icon: 'none', title: '商品链接异常，请联系客服！' })
  }
  console.log(productDetailsData.value.url)
  uni.navigateToMiniProgram({
    appId: import.meta.env.VITE_YOUZHAN_APPID,
    shortLink: productDetailsData.value.url,
    extraData: {}, // 可传递参数过去
    envVersion: 'release',
    success(res) {
      console.log('跳转成功')
    },
    fail(err) {
      console.log('跳转失败', err)
      if (err.errMsg.includes('cancel')) {
        return
      }
      if (err.errMsg.includes('invalid shortLink')) {
        return uni.showToast({ icon: 'none', title: '商品链接异常，请联系客服！' })
      }

      uni.showToast({ icon: 'none', title: '跳转失败，请稍后再试！' })
    },
  })
}

// 获取色系字典
function getLuckyDetail(id: string) {
  const { data, run } = useRequest<ILuckyDetailItem>(() => luckyDetail(id))
  run().then(() => {
    const gemstone = data.value.gemList.map((e: GemItem) => {
      return e.name
    })
    const list = { ...data.value, gemstones: gemstone.join(' ') }
    productDetailsData.value = list
  })
}

// 分享
onShareAppMessage(() => {
  return {
    title: productDetailsData.value?.name,
    path: `pages/productDetails/index?id=${detailId.value}`,
    imageUrl: defaultShareImage.value || '',
  }
})
</script>

<style lang="scss" scoped>
//商品图片开始位置
.productDetailsImagePalyClass {
  height: 750rpx;
  background: #f8eded;
}
//商品图片结束位置
//商品价格分类开始位置
.productDetailsContentClass {
  height: 340rpx;
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .goodsTextClass {
    padding: 30rpx;
    .goodsPriceClass {
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 57rpx;
      color: #fc3b3b;
      text-align: left;
    }
    .goodsTypeClass {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 45rpx;
      margin-top: 8rpx;
      background: rgba(117, 117, 117, 0.07);
      border-radius: 23rpx;
      .goodsTypeImagesClass {
        width: 30rpx;
        height: 23rpx;
      }
      .goodsTypeTextClass {
        font-family: SourceHanSerifCN;
        font-size: 22rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 22rpx;
      }
    }
  }
  .productDetailsTitleClass {
    margin: 0 30rpx;
    font-family: SourceHanSerifCN;
    font-size: 30rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 32rpx;
    color: #000000;
    text-align: left;
  }
  .productDetailsEnsureClass {
    height: 82rpx;
    margin: 25rpx 30rpx;
  }
  .productDetailsClassificationClass {
    margin: 0 30rpx;
    font-family: SourceHanSerifCN;
    font-size: 22rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 31rpx;
    color: #333333;
    text-align: left;
  }
}
//商品价格分类结束位置
//商品详情开始位置
.productDetailsTextClass {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .productDetailsTextTitleClass {
    padding: 30rpx;
    font-family: SourceHanSerifCN;
    font-size: 30rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 44rpx;
    color: #000000;
    text-align: left;
  }
  .productDetailsTextContentClass {
    padding: 0 30rpx 30rpx 30rpx;
    font-family: SourceHanSerifCN;
    font-size: 25rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 43rpx;
    color: #000000;
    text-align: left;
  }
}
//商品详情结束位置
//商品底部按钮开始位置
.productDetailsButtonBodyPlaceholderClass {
  height: 188rpx;
}
.productDetailsButtonBodyClass {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  height: 188rpx;
  background: #fff;
  .productDetailsButtonBodyInnerClass {
    display: flex;
    width: 100%;
    height: 100%;
    .productDetailsButtonBodyInnerLeftClass {
      display: flex;
      flex: 2;
      justify-content: center; // 水平居中
      background-color: #fff; // 可选，调试用;
      .productDetailsButtonBodyInnerLeftShareClass {
        width: 44rpx;
        height: 44rpx;
        //margin-top: 29rpx;
      }
      .productDetailsButtonBodyInnerLeftShareTextClass {
        font-family: SourceHanSerifCN;
        font-size: 20rpx;
        font-style: normal;
        font-weight: bold;
        line-height: 0;
        color: #1e2127;
        text-align: right;
      }
    }
    .productDetailsButtonBodyInnerRightClass {
      flex: 8;
      :deep() {
        //按钮样式
        .custom {
          width: 600rpx;
          height: 81rpx;
          margin: 20rpx 27rpx 0 0;
          background: #e73c3c;
        }
        //按钮圆角
        .wd-button.is-round {
          border-radius: 13rpx;
        }
        //按钮文字样式
        .wd-button__text {
          font-size: 32rpx;
          font-weight: bold;
          color: #fff9f3;
        }
      }
    }
  }
  //商品底部按钮结束位置
}
</style>
