import { http } from '@/utils/http'
// 分页查询订单参数类型
export interface IDesignQuery {
  sort?: Array<string>
  page: number
  size: number
}

export interface IDesignGroup {
  id: number
  designPicture: string
  createTime: string
  price: number
}

export interface IDesignPageItem {
  list: IDesignGroup[]
  total: number
}

/** GET 请求 分页查询设计 */
export const designPage = (data: IDesignQuery) => {
  return http.get<IDesignPageItem>('/design/page', { ...data })
}

/** GET 请求 分页查询设计 */
export const designDelete = (id: string) => {
  return http.post<boolean>(`/design/delete/${id}`)
}
