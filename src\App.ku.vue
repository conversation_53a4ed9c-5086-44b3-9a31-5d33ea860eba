<script setup lang="ts">
import loginModule from '@/components/loginModule.vue'
import { useLoginGuard } from '@/composables/useTheme'
import { getPhone, loginRun, getUserInfo } from '@/utils/login'
import { onHide } from '@dcloudio/uni-app'
const { showLoginModal, checkLogin } = useLoginGuard()
// 获取手机号码反馈
async function decryptPhoneNumber(e: any) {
  try {
    const code = e.code
    if (!code) {
      throw new Error('code 不存在')
    }

    await loginRun()
    await getPhone(code)
    await getUserInfo()

    uni.showToast({ icon: 'none', title: '登录成功' })
    checkLogin(false)
  } catch (err) {
    console.error('登录流程出错', err)
    uni.showToast({ icon: 'none', title: '登录失败，请重试' })
    checkLogin(false)
  }
}
function goToUserAgreement() {
  uni.navigateTo({
    url: `/pages/userAgreement/index`,
  })
}

function goToPrivacyPolicy() {
  uni.navigateTo({
    url: `/pages/privacyPolicy/index`,
  })
}
onHide(() => {
  if (showLoginModal) {
    checkLogin(false)
  }
})
</script>

<template>
  <view>
    <loginModule
      v-if="showLoginModal"
      cancelButtonText="不同意"
      confirmButtonText="同意"
      :phone="true"
      @decryptPhoneNumber="decryptPhoneNumber"
      @cancel="() => checkLogin(false)"
    >
      <slot name="main">
        <view class="text-30rpx font-[SourceHanSerifCN] lh-30rpx p-30rpx">
          你授权后，小程序开发者将收集你的手机号，为你提供相关服务。开发者严格按照
          <text class="text-blue-500 underline" @tap="goToUserAgreement">《用户协议》</text>
          、
          <text class="text-blue-500 underline" @tap="goToPrivacyPolicy">《隐私政策》</text>
          处理你的个人信息。
        </view>
      </slot>
    </loginModule>
    <KuRootView />
  </view>
</template>
