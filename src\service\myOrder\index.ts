import { http } from '@/utils/http'
// 分页查询订单参数类型
export interface IOrderPageQuery {
  sort?: Array<string>
  page: number
  size: number
}

// 分页查询订单接口返回参数类型
export interface IOrderItem {
  id: number
  name: string
  price: number
  picture: string
  num: number
}

export interface IOrderGroup {
  id: number
  list: IOrderItem[]
  logisticsId: number
  expressCode: string
  afterSalesReason: string
  cancelReason: string
  status: number
  total: number
  amount: number
  logisticsName: string
  createUser: string
  createTime: string
  address: string
}

export interface IOrderPageItem {
  list: IOrderGroup[]
  total: number
}

export let dataList: IOrderGroup | null = null

export function getActionData(obj: IOrderGroup) {
  dataList = obj
}

/** GET 请求 分页查询订单 */
export const orderPage = (data: IOrderPageQuery) => {
  return http.get<IOrderPageItem>('/order/page', { ...data })
}
