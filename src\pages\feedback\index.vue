<route lang="json5">
{
  style: {
    navigationBarTitleText: '意见反馈',
  },
}
</route>

<template>
  <view v-if="total">
    <view class="feedbackItem" v-for="item in feedbackItemData" :key="item.id">
      <view
        class="feedbackItemTag"
        style="color: #ffffff; background-color: #999999"
        v-if="item.status == 1"
      >
        已回复
      </view>
      <view
        class="feedbackItemTag"
        style="color: #ffffff; background-color: #fc3b3b"
        v-if="item.status == 0"
      >
        未回复
      </view>
      <view class="p-20rpx">
        <view class="feedbackItemContentCell">
          <view class="feedbackItemContent">反馈时间</view>
          <view class="feedbackItemContentDetails">{{ item.createTime }}</view>
        </view>
        <view class="feedbackItemContentCell">
          <view class="feedbackItemContent">反馈类型</view>
          <view class="feedbackItemContentDetails">{{ item.feedbackTypeName }}</view>
        </view>
        <view class="feedbackItemContentCell">
          <view class="feedbackItemContent">反馈内容</view>
          <view class="feedbackItemContentDetails">
            {{ item.content }}
          </view>
        </view>
        <view class="feedbackItemContentCell" v-if="item.status == 1">
          <view class="feedbackItemContent">回复内容</view>
          <view class="feedbackItemContentDetails">
            {{ item.answerContent }}
          </view>
        </view>
        <view class="text-right p-17rpx" v-if="item.status == 0">
          <wd-button
            type="info"
            custom-class="custom"
            :round="false"
            size="small"
            @tap.prevent="showMessageBox(item.id)"
          >
            撤回
          </wd-button>
        </view>
      </view>
    </view>

    <view class="p-25rpx text-center">
      <wd-button
        custom-class="customFeedback"
        type="error"
        :round="false"
        @click="goToFeedbackOnOpinions"
      >
        我要反馈
      </wd-button>
      <wd-loadmore
        :state="state"
        @reload="loadmore"
        :loading-props="{ color: '#e73c3c', size: 20 }"
      />
    </view>
  </view>
  <EmptyState v-else>
    <view class="lh-39rpx text-27rpx fw-bold font-[SourceHanSerifCN] c-#cac8c8 text-center">
      暂无反馈
    </view>
    <view class="text-center mt-20rpx">
      <wd-button type="error" :round="false" size="small" @click="goToFeedbackOnOpinions">
        去反馈
      </wd-button>
    </view>
  </EmptyState>
  <MessageBoxHb
    ref="MessageBoxHbElement"
    msg="确定要撤销吗？"
    cancelButtonText="我再想想"
    @confirm="confirm"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { onReachBottom } from '@dcloudio/uni-app'
import MessageBoxHb from '@/components/MessageBoxHb.vue'
import EmptyState from '@/components/EmptyState.vue'
import {
  IFeedbackListResponse,
  feedbackPage,
  IFeedbackPageQuery,
  feedbackDelete,
} from '@/service/feedbackOnOpinions/index'
// 反馈列表数据
const feedbackItemData = ref([])
const pages = ref<number>(1)
const state = ref<LoadMoreState>('finished')
const total = ref(0)
const activeId = ref(null)

// 引入消息框组件
const MessageBoxHbElement = ref<InstanceType<typeof MessageBoxHb> | null>(null)
// 显示消息框
function showMessageBox(id: string) {
  activeId.value = id
  if (MessageBoxHbElement.value) {
    MessageBoxHbElement.value.open()
  }
}

// 确认删除
function confirm() {
  const { data, run, code } = useRequest<boolean>(() => feedbackDelete(activeId.value))
  run().then(() => {
    if (code.value === '0') {
      uni.showToast({ icon: 'none', title: '撤销成功' })
      feedbackItemData.value = feedbackItemData.value.filter((item) => item.id !== activeId.value)
      total.value = feedbackItemData.value.length
    }
  })
}

// 跳转到反馈详情
const goToFeedbackOnOpinions = () => {
  // 跳转到反馈详情页面
  uni.navigateTo({
    url: '/pages/feedbackOnOpinions/index',
  })
}

// 页面加载时加载数据
onReachBottom(() => {
  if (feedbackItemData.value.length < total.value) {
    loadmore()
  } else {
    state.value = 'finished'
  }
})

// 加载更多数据
async function loadmore() {
  await getFeedbackPage()
  if (feedbackItemData.value.length >= total.value) {
    state.value = 'finished'
  } else {
    state.value = 'loading'
  }
}

// 获取订单信息
async function getFeedbackPage() {
  const parameter: IFeedbackPageQuery = { page: pages.value, size: 5 }
  const { data, run } = useRequest<IFeedbackListResponse>(() => feedbackPage(parameter))
  run().then(() => {
    pages.value++

    feedbackItemData.value = [...feedbackItemData.value, ...data.value.list]
    total.value = data.value.total
  })
}
onShow(() => {
  pages.value = 1
  feedbackItemData.value = []
  getFeedbackPage()
})
</script>

<style lang="scss" scoped>
// 意见反馈cell样式 开始位置
.feedbackItem {
  margin: 25rpx;
  background: #ffffff;
  border-radius: 15rpx;
  .feedbackItemTag {
    width: 110rpx;
    height: 46rpx;
    font-family: SourceHanSerifCN;
    font-size: 26rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 46rpx;
    text-align: center;
    background-color: #f3f3f3;
    border-radius: 15rpx 0 15rpx 0;
  }
  .feedbackItemContentCell {
    display: flex;
    padding-bottom: 20rpx;
    .feedbackItemContent {
      width: 200rpx;
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 30rpx;
      color: #666666;
      text-align: left;
    }
    .feedbackItemContentDetails {
      flex: 1;
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 30rpx;
      color: #1e2127;
      text-align: right;
    }
  }
}
// 意见反馈cell样式 结束位置

// 样式穿透css开始位置
:deep() {
  //按钮样式
  .custom {
    width: 135rpx;
    height: 56rpx;
    font-family: SourceHanSerifCN;
    font-size: 26rpx !important;
    font-style: normal !important;
    font-weight: 500 !important;
    line-height: 37rpx !important;
    color: #1e2127 !important;
  }

  .customFeedback {
    width: 690rpx;
    height: 86rpx;
    font-family: SourceHanSerifCN;
    font-size: 32rpx !important;
    font-style: normal !important;
    font-weight: 500 !important;
    line-height: 32rpx !important;
    color: #ffffff !important;
  }
}
// 样式穿透css结束位置
//无数据时按钮居中样式
.wrapper {
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  height: 100vh;
}
</style>
