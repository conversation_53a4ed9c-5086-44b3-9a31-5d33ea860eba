import { http } from '@/utils/http'

// 吉品标签类型
export interface ICustomTypeListItem {
  id: string
  name: string
  ordered: number
}
// 标签类型
export interface ITabsListItem {
  id: string
  name: string
  ordered: number
}
// 获取开运吉品商品类型
export interface ICustomPageQuery {
  sort?: Array<string>
  page?: number
  size?: number
  typeId?: string
}

export interface ICustomPageListItem {
  id: string
  name: string
  labels: string[]
  designPicture: string
  luckyTypeId: string
  ordered: number
  picture: string
  price: number
}
// 获取开运吉品商品类型
export interface ICustomPageItem {
  list: Array<ICustomPageListItem>
  total: number
}

// 宝石项
export interface GemItem {
  id: string
  name: string
  picture: string
}

export interface ICustomDetailItem {
  id: string
  name: string
  picture: string
  price: number
  url: string
  ordered: number
  labels: string[]
  luckyTypeId: string
  gemstones: string
  gemList: GemItem[]
  content: string
}

/** GET 请求 获取开运吉品标签 */
export const customTypeList = () => {
  return http.get<ICustomTypeListItem[]>('/custom/listType')
}

/** GET 请求 获取开运吉品商品 */
export const customPage = (data: ICustomPageQuery) => {
  return http.get<ICustomPageItem>('/custom/page', { ...data })
}

/** GET 请求 获取定制商品详情 */
export const customDetail = (id: string) => {
  return http.get<ICustomDetailItem>(`/custom/detail/${id}`)
}

/** GET 请求 获取用户总数 */
export const commonTotalUser = () => {
  return http.get<number>('/common/totalUser')
}
