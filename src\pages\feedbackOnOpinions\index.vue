<route lang="json5">
{
  style: {
    navigationBarTitleText: '意见反馈',
  },
}
</route>

<template>
  <view class="m-30rpx">
    <view class="feedbackOnOpinionsTitle">
      反馈类型
      <text class="feedbackOnOpinionsTitleIcon">*</text>
    </view>
    <view class="mt-30rpx">
      <wd-row>
        <wd-col :span="6" v-for="item in options" :key="item.id">
          <view
            :class="
              item.id == feedbackItemData.feedbackTypeId ? 'optionClassActive' : 'optionClass'
            "
            @click="feedbackItemData.feedbackTypeId = item.id"
          >
            {{ item.name }}
          </view>
        </wd-col>
      </wd-row>
    </view>
    <view class="feedbackOnOpinionsTitle mt-30rpx">
      反馈描述
      <text class="feedbackOnOpinionsTitleIcon">*</text>
    </view>
    <view class="feedbackOnOpinionsContent">
      <wd-textarea v-model="feedbackItemData.content" placeholder="" />
    </view>
    <view class="feedbackOnOpinionsTitle mt-120rpx">上传图片</view>
    <view class="mt-30rpx">
      <wd-upload :file-list="fileList" multiple :action="action" @change="handleChange"></wd-upload>
    </view>
    <view class="h-120rpx"></view>
    <view class="ButtonBodyClass">
      <view class="text-center">
        <wd-button
          custom-class="customFeedback"
          type="error"
          :round="false"
          @click="addFeedbackAdd"
        >
          提交反馈
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import {
  ITypeListItem,
  feedbackTypeList,
  feedbackAdd,
  IFeedbackAddQuery,
} from '@/service/feedbackOnOpinions/index'
import { getEnvBaseUploadUrl } from '@/utils'

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`

const feedbackItemData = ref<IFeedbackAddQuery>({
  feedbackTypeId: '',
  content: '',
  picture: [],
})

const options = ref<ITypeListItem[]>()
// 获取反馈那些
function getFeedbackTypeList() {
  const { data, run } = useRequest<ITypeListItem[]>(() => feedbackTypeList())
  run().then(() => {
    options.value = data.value
  })
}
const fileList = ref([])
const action = ref(VITE_UPLOAD_BASEURL)
// 获取上传图片信息
function handleChange({ fileList: files }) {
  feedbackItemData.value.picture = files.map((e) => {
    return JSON.parse(e.response).data.url
  })
  fileList.value = files
}

// 添加反馈信息
function addFeedbackAdd() {
  const { feedbackTypeId, content } = feedbackItemData.value
  if (!feedbackTypeId) {
    return uni.showToast({ icon: 'none', title: '请先选择反馈类型，再提交！' })
  }
  if (!content) {
    return uni.showToast({ icon: 'none', title: '请先填写反馈内容，再提交！' })
  }
  const { data, run } = useRequest<boolean>(() => feedbackAdd(feedbackItemData.value))
  run().then(() => {
    if (data.value) {
      uni.showToast({ icon: 'none', title: '感谢您的反馈！' })
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    } else {
      return uni.showToast({ icon: 'none', title: '反馈失败，请重新反馈！' })
    }
  })
}

onShow(() => {
  getFeedbackTypeList()
})
</script>

<style lang="scss" scoped>
.feedbackOnOpinionsTitle {
  font-family: SourceHanSerifCN;
  font-size: 30rpx;
  font-style: normal;
  font-weight: bold;
  line-height: 39rpx;
  color: #000000;
  .feedbackOnOpinionsTitleIcon {
    font-family: SourceHanSerifCN;
    font-size: 28rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 39rpx;
    color: #f53535;
  }
}
.optionClass {
  width: 159rpx;
  height: 68rpx;
  font-family: SourceHanSerifCN;
  font-size: 28rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 68rpx;
  color: #000000;
  text-align: center;
  background: #f3f3f3;
  border-radius: 10rpx;
}

.optionClassActive {
  width: 159rpx;
  height: 68rpx;
  font-family: SourceHanSerifCN;
  font-size: 28rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 68rpx;
  color: #e73c3c;
  text-align: center;
  background: #fff2f2;
  border-radius: 10rpx;
}

.feedbackOnOpinionsContent {
  width: 690rpx;
  height: 270rpx;
  margin-top: 30rpx;
  background: #f3f3f3;
  border-radius: 20rpx;
}
:deep(.wd-textarea) {
  background: #f3f3f3 !important;
  border-radius: 20rpx;
}

:deep(.wd-textarea__inner) {
  background: #f3f3f3 !important;
}
:deep(.wd-upload__preview) {
  width: 200rpx !important;
  height: 200rpx !important;
  border-radius: 20rpx !important;
}

:deep(.wd-upload__picture) {
  width: 200rpx !important;
  height: 200rpx !important;
  border-radius: 20rpx !important;
}

:deep(.wd-upload__evoke) {
  width: 200rpx !important;
  height: 200rpx !important;
  border-radius: 20rpx !important;
}

:deep() {
  //按钮样式
  .customFeedback {
    width: 690rpx;
    height: 86rpx;
    font-family: SourceHanSerifCN;
    font-size: 32rpx !important;
    font-style: normal !important;
    font-weight: 500 !important;
    line-height: 32rpx !important;
    color: #ffffff !important;
  }
}
.ButtonBodyClass {
  position: fixed;
  right: 0;
  bottom: 30rpx;
  left: 0;
  z-index: 100;
  height: 86rpx;
  background: #fff;
}
</style>
