<template>
  <wd-action-sheet v-model="show" :close-on-click-modal="false" :safe-area-inset-bottom="safeArea">
    <view class="block" :class="{ my54rpx: !safeArea, mt54rpx: safeArea }">
      <view class="blockTitle">{{ props.title }}</view>
      <slot>
        <view class="blockContent">{{ props.msg }}</view>
      </slot>
      <view class="blockContentButton">
        <view class="blockContentButtonInner">
          <view class="blockContentButtonInnerLeft" v-if="props.cancelButton" @click="cancel">
            <wd-button
              custom-class="w240rpx h79rpx rounded-15rpx!"
              :round="false"
              type="info"
              plain
            >
              {{ props.cancelButtonText }}
            </wd-button>
          </view>
          <view class="blockContentButtonInnerRight" v-if="props.confirmButton">
            <wd-button
              v-if="phone"
              custom-class="w240rpx h79rpx rounded-15rpx!"
              :round="false"
              open-type="getPhoneNumber"
              :loading="loading"
              @click="openLoading"
              @getphonenumber="decryptPhoneNumber"
            >
              {{ props.confirmButtonText }}
            </wd-button>
            <wd-button
              v-else
              custom-class="w240rpx h79rpx rounded-15rpx!"
              :round="false"
              @click="confirm"
            >
              {{ props.confirmButtonText }}
            </wd-button>
          </view>
        </view>
      </view>
    </view>
  </wd-action-sheet>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useEnvironmentStore } from '@/store'
defineOptions({ name: 'MessageBoxHb' })
const emit = defineEmits(['confirm', 'cancel', 'decryptPhoneNumber'])
const environmentStore = useEnvironmentStore()

const safeArea = computed(() => environmentStore.environmentInfo.isEnvironment === 'ios')

// prop基本属性
const props = defineProps({
  msg: {
    type: String,
    default: '确认操作？',
  },
  title: {
    type: String,
    default: '提示',
  },
  confirmButtonText: {
    type: String,
    default: '确定',
  },
  cancelButtonText: {
    type: String,
    default: '取消',
  },
  cancelButton: {
    type: Boolean,
    default: true,
  },
  confirmButton: {
    type: Boolean,
    default: true,
  },
  phone: {
    type: Boolean,
    default: false,
  },
})
const show = ref(props.phone)
const loading = ref(false)

// 关闭
function close() {
  show.value = false
}
// 打开
function open() {
  show.value = true
}
// 确认按钮点击事件
function confirm() {
  emit('confirm')
}
// 取消按钮点击事件
function cancel() {
  emit('cancel')
}
// 关闭加载
function closeLoading() {
  loading.value = false
}
// 打开加载
function openLoading() {
  loading.value = true
}

// 获取手机号码
function decryptPhoneNumber(e) {
  emit('decryptPhoneNumber', e)
}

// 暴露 open 方法给父组件调用
defineExpose({ open, close, closeLoading })
</script>

<style lang="scss" scoped>
.block {
  .blockTitle {
    padding-left: 14rpx;
    margin-top: 54rpx;
    font-family: SourceHanSerifCN;
    font-size: 32rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 34rpx;
    color: #a92525;
    text-align: center;
  }
  .blockContent {
    padding: 0 14rpx;
    margin-top: 62rpx;
    font-family: SourceHanSerifCN;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 32rpx;
    color: #000000;
    text-align: center;
  }
  .blockContentButton {
    text-align: center;
    .blockContentButtonInner {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 60rpx;
      .blockContentButtonInnerLeft {
        flex: 1;
        .blockContentButtonInnerLeftInner {
          width: 270rpx;
          height: 80rpx;
          margin: 0 auto;
          font-family: SourceHanSerifCN;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 80rpx;
          color: #000000;
          border: 1rpx solid #aaaaaa;
          border-radius: 13rpx;
        }
      }
      .blockContentButtonInnerLeftInner:active {
        background-color: rgba(0, 0, 0, 0.1); // 半透明点击态
      }
      .blockContentButtonInnerRight {
        flex: 1;
        .blockContentButtonInnerRightInner {
          width: 270rpx;
          height: 80rpx;
          margin: 0 auto;
          font-family: SourceHanSerifCN;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 80rpx;
          color: #ffffff;
          background: #e73c3c;
          border-radius: 13rpx;
        }
        .blockContentButtonInnerRightInner:active {
          background-color: rgba(231, 60, 60, 0.8);
        }
      }
    }
  }
}
</style>
