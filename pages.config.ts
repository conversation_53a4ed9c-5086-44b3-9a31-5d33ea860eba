import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'unibest',
    navigationBarBackgroundColor: '#FFFFFF',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  tabBar: {
    color: '#999999',
    selectedColor: '#E63B3C',
    backgroundColor: '#FFFFFF',
    borderStyle: 'black',
    height: '50px',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    list: [
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/homeHL.png',
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/diy.png',
        selectedIconPath: 'static/tabbar/diyHL.png',
        pagePath: 'pages/diy/diy',
        text: 'DIY',
      },
      {
        iconPath: 'static/tabbar/gemLibrary.png',
        selectedIconPath: 'static/tabbar/gemLibraryHL.png',
        pagePath: 'pages/gemLibrary/gemLibrary',
        text: '宝石库',
      },
      {
        iconPath: 'static/tabbar/mine.png',
        selectedIconPath: 'static/tabbar/mineHL.png',
        pagePath: 'pages/about/about',
        text: '我的',
      },
    ],
  },
})
