// 全局要用的类型放到这里

declare global {
  type IResData<T> = {
    code: number
    msg: string
    data: T
  }

  // uni.uploadFile文件上传参数
  type IUniUploadFileOptions = {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  type IUserInfo = {
    nickName?: string
    avatar?: string
    phone?: string
    gender?: string
    openid?: string
    token?: string
    customized?: boolean
  }

  type IEnvironmentInfo = {
    isEnvironment?: string
  }
}

export {} // 防止模块污染
