<route lang="json5">
{
  style: {
    navigationBarTitleText: '定制详情',
  },
}
</route>

<template>
  <view
    class="body"
    :style="{
      backgroundImage: `url(${getImagesUrl('6859659de4b00e5b2cf36191.png')})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
    }"
  >
    <view class="content">
      <!-- Canvas 预览区域 -->
      <view class="canvas-container">
        <!-- 网格背景辅助线 -->
        <view class="grid-background"></view>
        <canvas
          ref="myCanvasRef"
          canvas-id="previewCanvas"
          id="previewCanvas"
          type="2d"
          :class="state.canvasClass"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        ></canvas>
      </view>

      <view
        v-if="!detailId"
        class="w110rpx h56rpx bg-#fff rounded-13rpx text-center text-27rpx lh-56rpx font-[SourceHanSerifCN] fw-bold mt-20rpx active:bg-#f5f5f5 transition-all duration-200"
        @click="onSave"
      >
        保存
      </view>
      <view
        v-if="isEdit"
        class="w110rpx h56rpx bg-#fff rounded-13rpx text-center text-27rpx lh-56rpx font-[SourceHanSerifCN] fw-bold mt-20rpx active:bg-#f5f5f5 transition-all duration-200"
        @click="goEdit"
      >
        编辑
      </view>
    </view>
    <view class="my-40rpx mx-20rpx rounded-20rpx bg-#fff py-36rpx px-26rpx">
      <view class="lh-32rpx text-32rpx fw-bold font-[SourceHanSerifCN] c-#000000">定制详情</view>
      <view class="lh-35rpx text-24rpx fw-bold font-[SourceHanSerifCN] c-#333333 mt-26rpx flex">
        <view class="flex-1">
          颗数:{{ customizationDetailsData.totalCount }} 周长:{{
            customizationDetailsData.circumference
          }}mm
        </view>
        <view class="flex-1 text-right">
          推荐{{ customizationDetailsData.recommendedGender }}佩戴
        </view>
      </view>
    </view>
    <view class="my-40rpx mx-20rpx rounded-20rpx bg-#fff py-36rpx px-26rpx">
      <view class="flex lh-57rpx itemBottomBorderClass">
        <view
          class="text-26rpx fw-bold font-[SourceHanSerifCN] c-#808080 text-left flex items-center"
        >
          <view class="i-carbon-warning h30rpx w30rpx" />
          实际金额以人工客服为准
        </view>
        <view class="flex-1 text-right flex justify-end items-center">
          <text class="text-32rpx fw-bold font-[SourceHanSerifCN] c-#000000 text-right">
            预估总价:
          </text>
          <text class="fw-bold font-[SourceHanSerifCN] c-#dd0006 pl-10rpx">
            <text class="text-26rpx">¥</text>
            <text class="text-40rpx">{{ customizationDetailsData.totalCost }}</text>
          </text>
        </view>
      </view>
      <view class="lh-32rpx text-32rpx fw-bold font-[SourceHanSerifCN] c-#000000 mt-30rpx">
        明细
      </view>
      <view
        class="flex lh-32rpx text-26rpx fw-bold font-[SourceHanSerifCN] c-#000000 mt36rpx"
        v-for="(item, index) in customizationDetailsData.beadDetails"
        :key="index"
      >
        <view class="flex-1">{{ item.code }}&nbsp;&nbsp;{{ item.name }}x{{ item.count }}</view>
        <view class="text-right">￥{{ item.unitPrice }}/颗</view>
      </view>
    </view>
    <view class="productDetailsButtonBodyPlaceholderClass"></view>
    <view class="productDetailsButtonBodyClass">
      <view class="productDetailsButtonBodyInnerClass" v-if="isShare">
        <view class="productDetailsButtonBodyInnerLeftClass">
          <wd-button plain custom-class="custom1" @click="goEdit">
            <image
              class="h32rpx w32rpx pr-20rpx"
              src="../../static/tabbar/diyHL.png"
              mode="widthFix"
            />
            我要修改
          </wd-button>
        </view>
        <view class="productDetailsButtonBodyInnerRightClass" @click="showMessageBox">
          <wd-button custom-class="custom2">
            <view class="i-carbon-headset pr-20rpx" />
            联系客服
          </wd-button>
        </view>
      </view>
      <view class="productDetailsButtonBodyInnerClass" v-else>
        <view class="productDetailsButtonBodyInnerLeftClass">
          <wd-button v-if="!detailId" plain custom-class="custom1" @click="handleShare">
            <image
              class="h32rpx w32rpx pr-20rpx"
              src="../../static/images/shareIconHl.png"
              mode="widthFix"
            />
            分享设计
          </wd-button>
          <wd-button v-else plain custom-class="custom1" open-type="share">
            <image
              class="h32rpx w32rpx pr-20rpx"
              src="../../static/images/shareIconHl.png"
              mode="widthFix"
            />
            分享设计
          </wd-button>
        </view>
        <view class="productDetailsButtonBodyInnerRightClass">
          <wd-button custom-class="custom2" @click="showMessageBox">
            <view class="i-carbon-checkbox-checked pr-20rpx" />
            下单制作
          </wd-button>
        </view>
      </view>
    </view>
    <CustomerService ref="CustomerServiceElement" />
    <MessageBoxHb
      ref="MessageBoxHbElement"
      :msg="msg"
      cancelButtonText="确定"
      :confirmButton="false"
    />
  </view>
</template>

<script lang="ts" setup>
import MessageBoxHb from '@/components/MessageBoxHb.vue'
import { ref, reactive, onBeforeUnmount } from 'vue'
import { getImagesUrl } from '@/utils/getImagesUrl'
import { useUserStore } from '@/store'
import CustomerService from '@/components/CustomerService.vue'
import { maskPhone } from '@/utils'
import { designAdd, getDesignDetail } from '@/service/custom'
import { uploadFile2 } from '@/hooks/useUpload'
import { getBannerList } from '@/utils/getBannerList'

defineOptions({
  name: 'customizationDetails',
})

const userStore = useUserStore()
const isNickname = computed(() => {
  if (userStore.userInfo?.nickName) {
    return userStore.userInfo?.nickName
  } else if (userStore.userInfo?.phone) {
    return maskPhone(userStore.userInfo?.phone)
  } else {
    return '微信用户'
  }
})

// 引入消息框组件
const MessageBoxHbElement = ref<InstanceType<typeof MessageBoxHb> | null>(null)
const msg = ref('')
const detailId = ref('')
// 默认分享图片
const defaultShareImage = ref('')

// 获取默认分享图
const fetchDefaultShareImage = async () => {
  try {
    const list = await getBannerList('10')
    if (Array.isArray(list) && list.length > 0) {
      defaultShareImage.value = list[0].picture || ''
    }
  } catch (e) {
    console.error('获取默认分享图失败', e)
  }
}
// 分享
onShareAppMessage(() => {
  console.log('!11111', !detailId.value)
  if (!detailId.value) {
    msg.value = '请先保存设计后再分享'
    MessageBoxHbElement.value.open()
    return null
  }
  return {
    title: `${isNickname.value}定制详情`,
    path: `pages/customizationDetails/index?id=${detailId.value}&share=1`,
    imageUrl: defaultShareImage.value || customizationDetailsData.value.designPicture || '',
  }
})
const handleShare = () => {
  if (!detailId.value) {
    msg.value = '请先保存设计后再分享'
    MessageBoxHbElement.value.open()
  }
}

const isShare = ref(false) // 是否通过分享进入
const isEdit = ref(false) // 是否显示编辑按钮
onLoad((options) => {
  const { id } = options
  detailId.value = id
  getDetailData()
  // 预取默认分享图
  fetchDefaultShareImage()
  // 判断是否通过分享进入
  const shareFlag = String(options?.share ?? '')
  console.log(options, shareFlag, 'jjjjjj')

  if (shareFlag === '1') {
    console.log('用户通过分享进入小程序')
    isShare.value = true
  } else {
    isShare.value = false
  }
  if (options?.isEdit === '1') {
    isEdit.value = true
  } else {
    isEdit.value = false
  }
})

onShow(() => {
  ;(uni as any).hideShareMenu()
})

const getDetailData = async () => {
  if (detailId.value) {
    const { data, run } = useRequest<void>(() => getDesignDetail(detailId.value))
    await run()
    const { summaryInfo, beads } = JSON.parse((data.value as any).bracelets)
    const { setData } = useData()

    setData('zydz', {
      beads,
      summaryInfo,
    })
    console.log('data', data.value, beads)
    customizationDetailsData.value = summaryInfo
    state.beads = beads
    state.totalBeads = beads.length
    state.nextAddPosition = -1
    state.beadInfoArray = []
    state.actualRadius = null
  }
  initCanvas()
}

const goEdit = () => {
  const { setData } = useData()
  setData('dzxq', {
    beads: state.beads,
    summaryInfo: customizationDetailsData.value,
  })
  uni.navigateTo({
    url: `/pages/diy/modules/freeCustomize`,
  })
}

// Canvas 相关变量
let canvasEl: any = null
let ctx: any = null
// 记录设备像素比例，用于逻辑尺寸换算
let dprScale = 1

const clipRatio = 0.98 // 珠子图片所占孔位比例
const holeClipRatio = 0.94 // 空孔所占比例，稍小以产生细微缝隙

// 图片缓存
const beadImageCache: Record<string, any> = {}

// 判断图片是否为圆形（宽高比接近 1）
const isCircularBead = (image: any) => {
  if (!image || !image.width || !image.height) return true
  const aspectRatio = image.width / image.height
  return aspectRatio >= 0.8 && aspectRatio <= 1.25
}

// 计算图片实际显示尺寸（长方形沿高度适配）
const calculateActualImageSize = (bead: any, beadSize: number) => {
  if (!bead || bead.isEmptyHole || !bead.imagePath) {
    return { width: beadSize * 2, height: beadSize * 2 }
  }

  const cacheKey = bead.imagePath
  const cached = beadImageCache[cacheKey]
  if (cached && cached.width && cached.height) {
    const imgW = cached.width
    const imgH = cached.height

    if (isCircularBead(cached)) {
      const maxSide = beadSize * 2
      const scale = Math.min(maxSide / imgW, maxSide / imgH)
      return { width: imgW * scale, height: imgH * scale }
    }

    const circularHeight = beadSize * 2
    const scale = circularHeight / imgH
    return { width: imgW * scale, height: circularHeight }
  }

  return { width: beadSize * 2, height: beadSize * 2 }
}

// 加载图片并缓存
const loadBeadImage = (url: string, cb: (img: any | null) => void) => {
  if (beadImageCache[url]) {
    cb(beadImageCache[url])
    return
  }
  const img = canvasEl?.canvas?.createImage?.()
  if (!img) {
    cb(null)
    return
  }
  img.onload = () => {
    beadImageCache[url] = img
    cb(img)
  }
  img.onerror = () => cb(null)
  img.src = url
}

// 绘制图片（支持长方形、旋转、圆角阴影）
const drawImageWithClipping = (
  image: any,
  x: number,
  y: number,
  beadSize: number,
  rotationAngle = 0,
) => {
  if (!image || !ctx) return

  ctx.save()
  ctx.translate(x, y)
  ctx.rotate(rotationAngle)

  const imgW = image.width || beadSize * 2
  const imgH = image.height || beadSize * 2

  let drawW: number, drawH: number
  if (isCircularBead(image)) {
    const maxSide = beadSize * 2
    const scale = Math.min(maxSide / imgW, maxSide / imgH)
    drawW = imgW * scale
    drawH = imgH * scale
  } else {
    const circularHeight = beadSize * 2
    const scale = circularHeight / imgH
    drawW = imgW * scale
    drawH = circularHeight
    if (drawW > beadSize * 2) {
      const maxScale = (beadSize * 2) / imgW
      drawW = beadSize * 2
      drawH = imgH * maxScale
    }
    if (drawW < beadSize * 2) {
      const minScale = (beadSize * 2) / imgW
      drawW = beadSize * 2
      drawH = imgH * minScale
    }
  }

  ctx.drawImage(image, -drawW / 2, -drawH / 2, drawW, drawH)
  ctx.restore()
}

// 节流重绘，避免高频闪烁
let frameRequested = false
const scheduleDraw = () => {
  if (frameRequested) return
  frameRequested = true
  const raf =
    canvasEl && canvasEl.canvas && typeof canvasEl.canvas.requestAnimationFrame === 'function'
      ? canvasEl.canvas.requestAnimationFrame.bind(canvasEl.canvas)
      : (cb: any) => setTimeout(cb, 16)
  raf(() => {
    frameRequested = false
    drawBracelet()
  })
}

// 绘制圆角矩形（用于长方形珠子阴影）
function drawRoundedRect(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number,
  radius: number,
) {
  const r = Math.max(0, Math.min(radius, Math.min(width, height) / 2))
  ctx.beginPath()
  ctx.moveTo(x + r, y)
  ctx.lineTo(x + width - r, y)
  ctx.quadraticCurveTo(x + width, y, x + width, y + r)
  ctx.lineTo(x + width, y + height - r)
  ctx.quadraticCurveTo(x + width, y + height, x + width - r, y + height)
  ctx.lineTo(x + r, y + height)
  ctx.quadraticCurveTo(x, y + height, x, y + height - r)
  ctx.lineTo(x, y + r)
  ctx.quadraticCurveTo(x, y, x + r, y)
  ctx.closePath()
  ctx.fill()
}

// 状态管理
const state = reactive({
  beadSize: 20,
  totalBeads: 18,
  maxBeads: 40,
  selectedBeads: 4,
  selectedIndex: -1, // 预览模式下不需要选中功能
  beads: [], // 珠子数组
  gapAngle: (Math.PI / 180) * 0, // 与定制页保持一致，无额外间隙，空孔靠 holeClipRatio 控制
  touchPosition: null,
  scale: 1,
  rotation: 0,
  lastTouches: [],
  transformOrigin: { x: 150, y: 150 },
  canvasReady: false,
  animationTimer: null,
  translateY: 0,
  isInAnim: false,
  moveAnima: null,
  canvasClass: 'bracelet-canvas',
  nextAddPosition: -1,
  beadInfoArray: [],
  actualRadius: null,
  isProcessingDrawTasks: false,
  beadDrawTaskTimer: null,
})

// 定制页面数据
const customizationDetailsData = ref({
  id: '',
  totalCount: '',
  circumference: '',
  recommendedGender: '',
  totalCost: '',
  gender: '',
  beadDetails: [],
  designPicture: '',
})
const instance = getCurrentInstance()
// 保存 / 编辑
const onSave = () => {
  setTimeout(() => {
    uni.canvasToTempFilePath(
      {
        canvas: canvasEl.canvas,
        canvasId: 'previewCanvas',
        fileType: 'png',
        quality: 1,
        success: async (res) => {
          console.log('生成成功', res) // 开始上传
          const imgRes = await uploadFile2<any>(res.tempFilePath)
          const imageUrl = imgRes.data.url
          console.log('imageUrl', imageUrl)
          customizationDetailsData.value.designPicture = imageUrl // 保存设计图片
          if (detailId.value) {
            // 编辑
          } else {
            onAdd()
          }
        },
        fail: (err) => {
          console.error('生成设计图失败：', err)
          uni.showToast({ icon: 'none', title: '生成设计图失败，请稍后重试!' })
        },
      },
      instance,
    )
  }, 300)
}

const onAdd = async () => {
  const params = {
    id: '',
    gemNum: customizationDetailsData.value.totalCount,
    perimeter: customizationDetailsData.value.circumference,
    gender: customizationDetailsData.value.gender,
    designPicture: customizationDetailsData.value.designPicture,
    referencePrice: customizationDetailsData.value.totalCost,
    bracelets: JSON.stringify({
      beads: state.beads,
      summaryInfo: customizationDetailsData.value,
    }),
    designDetailList: state.beads.map((item: any) => {
      return {
        name: item.name,
        picture: item.image,
        // diameter: item.size,
        price: item.price,
        quantity: item.size,
        skuId: item.uniqueId,
      }
    }),
    designDetailSortList: state.beads.map((bead) => bead.skuId),
    skuId: state.beads.map((bead) => bead.skuId),
  }
  const { data, run } = useRequest<any>(() => designAdd(params))
  run().then((res) => {
    msg.value = '保存成功'
    MessageBoxHbElement.value.open()
    detailId.value = data.value
    uni.showShareMenu() // 打开右上角分享
    // setTimeout(() => {
    //   uni.navigateBack()
    // }, 1000)
  })
}

const initPreviewBeads = () => {
  const { getData } = useData()

  const data = getData('zydz')
  state.beads = data.beads
  customizationDetailsData.value = data.summaryInfo
  state.totalBeads = 18
  state.nextAddPosition = -1
  state.beadInfoArray = []
  state.actualRadius = null

  console.log('珠子数量:', state.beads.length)
}

// 触摸事件处理函数（仅支持缩放和移动，不支持选择）
const handleTouchStart = (event: any) => {
  if (event.touches && event.touches.length > 0) {
    state.lastTouches = []
    for (let i = 0; i < event.touches.length; i++) {
      state.lastTouches.push({
        pageX: event.touches[i].pageX,
        pageY: event.touches[i].pageY,
      })
    }
  }
}

const handleTouchMove = (event: any) => {
  if (!event.touches || event.touches.length === 0) return

  // 双指缩放
  if (event.touches.length === 2 && state.lastTouches.length === 2) {
    const currentDistance = Math.sqrt(
      Math.pow(event.touches[0].pageX - event.touches[1].pageX, 2) +
        Math.pow(event.touches[0].pageY - event.touches[1].pageY, 2),
    )

    const lastDistance = Math.sqrt(
      Math.pow(state.lastTouches[0].pageX - state.lastTouches[1].pageX, 2) +
        Math.pow(state.lastTouches[0].pageY - state.lastTouches[1].pageY, 2),
    )

    if (lastDistance > 0) {
      const scaleChange = currentDistance / lastDistance
      let newScale = state.scale * scaleChange

      // 限制缩放范围
      newScale = Math.max(0.5, Math.min(3, newScale))
      state.scale = newScale

      if (state.canvasReady) {
        drawBracelet()
      }
    }
  }

  // 更新触摸点
  state.lastTouches = []
  for (let i = 0; i < event.touches.length; i++) {
    state.lastTouches.push({
      pageX: event.touches[i].pageX,
      pageY: event.touches[i].pageY,
    })
  }
}

const handleTouchEnd = () => {
  state.lastTouches = []
}

// 绘制珠子，支持图片旋转，使孔道朝向手串中心
const drawColorBead = (
  x: number,
  y: number,
  beadSize: number,
  bead: any,
  rotationAngle: number = 0,
) => {
  if (!ctx) {
    console.log('无法绘制珠子，ctx不存在')
    return
  }

  console.log('绘制珠子:', { x, y, beadSize, bead: bead.name })

  // 如果有图片，尝试绘制图片
  if (bead.image || bead.imagePath) {
    const imageUrl = bead.imagePath || bead.image

    // 创建图片对象
    const img = canvasEl.canvas.createImage()
    img.onload = () => {
      console.log('图片加载成功，开始绘制')
      ctx.save()

      // 将原点移动到珠子中心并旋转
      ctx.translate(x, y)
      ctx.rotate(rotationAngle)

      // 创建圆形裁剪区域
      ctx.beginPath()
      ctx.arc(0, 0, beadSize, 0, Math.PI * 2)
      ctx.clip()

      // 绘制图片
      ctx.drawImage(img, -beadSize, -beadSize, beadSize * 2, beadSize * 2)

      ctx.restore()
    }
    img.onerror = () => {
      drawColorCircle(x, y, beadSize, bead.color || '#FFD700')
    }
    img.src = imageUrl
  } else {
    // 没有图片，使用颜色绘制
    drawColorCircle(x, y, beadSize, bead.color || '#FFFFFF')
  }
}

// 绘制纯色圆圈
const drawColorCircle = (x: number, y: number, beadSize: number, color: string) => {
  // 绘制珠子主体
  ctx.beginPath()
  ctx.arc(x, y, beadSize, 0, Math.PI * 2)
  ctx.fillStyle = color
  ctx.fill()

  // 绘制边框
  // ctx.beginPath()
  // ctx.arc(x, y, beadSize, 0, Math.PI * 2)
  // ctx.strokeStyle = '#000'
  // ctx.lineWidth = 2
  // ctx.stroke()
}

// 绘制手串主函数
const drawBracelet = () => {
  if (!ctx || !canvasEl || !state.canvasReady) {
    console.log('Canvas未准备好，跳过绘制', {
      ctx: !!ctx,
      canvasEl: !!canvasEl,
      canvasReady: state.canvasReady,
    })
    return
  }

  console.log('开始绘制手串')
  ctx.clearRect(0, 0, canvasEl.canvas.width, canvasEl.canvas.height)

  // 设置透明背景
  ctx.fillStyle = 'rgba(255, 255, 255, 0.0)'
  ctx.fillRect(0, 0, canvasEl.canvas.width, canvasEl.canvas.height)

  // 绘制手串 - 使用逻辑尺寸（除以 dpr），避免双倍缩放导致坐标偏移
  const logicalWidth = canvasEl.canvas.width / dprScale
  const logicalHeight = canvasEl.canvas.height / dprScale
  const centerX = logicalWidth / 2
  const centerY = logicalHeight / 2
  const radius = Math.min(logicalWidth, logicalHeight) * 0.25

  console.log('绘制参数:', {
    centerX,
    centerY,
    radius,
    beadCount: state.beads.length,
    canvasWidth: canvasEl.canvas.width,
    canvasHeight: canvasEl.canvas.height,
  })

  drawBeadHoles(centerX, centerY, radius)

  console.log('手串绘制完成')
}

// 绘制珠子孔位
const drawBeadHoles = (centerX: number, centerY: number, radius: number) => {
  // 如果还未计算位置，先执行位置计算
  if (!ctx || !canvasEl) return

  if (!state.beadInfoArray || state.beadInfoArray.length === 0) {
    recalculateBeadPositions()
  }

  const drawTasks: any[] = []

  state.beadInfoArray.forEach((info: any) => {
    const bead = state.beads[info.index]
    if (!bead) return

    const x = info.centerX
    const y = info.centerY
    const beadSize = info.beadSize

    const isSingleBead = state.beadInfoArray.length === 1
    const vectorX = isSingleBead ? 0 : centerX - x
    const vectorY = isSingleBead ? 1 : centerY - y
    const magnitude = Math.sqrt(vectorX * vectorX + vectorY * vectorY) || 1
    const normalizedX = vectorX / magnitude
    const normalizedY = vectorY / magnitude

    let shadowIsCircle = true
    let cachedImg: any = null
    if (bead.imagePath) {
      const cacheKey = `${bead.imagePath}&uid=${bead.uniqueId || ''}&idx=${info.index}`
      cachedImg = beadImageCache[cacheKey]
      if (cachedImg) shadowIsCircle = isCircularBead(cachedImg)
      else shadowIsCircle = false
    }

    const shadowLayers = 6
    const maxShadowWidth = beadSize * 0.25

    for (let j = 0; j < shadowLayers; j++) {
      const layerAlpha = 0.35 - j * 0.05
      const expand = maxShadowWidth * (j / shadowLayers)
      const offsetRatio = j * 0.025
      const offsetX = x + normalizedX * offsetRatio * beadSize
      const offsetY = y + normalizedY * offsetRatio * beadSize

      ctx.beginPath()
      ctx.fillStyle = `rgba(244, 67, 54, ${layerAlpha})`

      if (shadowIsCircle) {
        ctx.arc(offsetX, offsetY, beadSize + expand, 0, Math.PI * 2)
        ctx.fill()
      } else if (cachedImg) {
        // 计算长方形尺寸
        const imgW = cachedImg.width
        const imgH = cachedImg.height
        const circularHeight = beadSize * 2
        const scale = circularHeight / imgH
        let drawW = imgW * scale
        let drawH = circularHeight

        if (drawW > beadSize * 2) {
          const maxScale = (beadSize * 2) / imgW
          drawW = beadSize * 2
          drawH = imgH * maxScale
        }
        if (drawW < beadSize * 2) {
          const minScale = (beadSize * 2) / imgW
          drawW = beadSize * 2
          drawH = imgH * minScale
        }

        ctx.save()
        ctx.translate(offsetX, offsetY)
        ctx.rotate(info.centerAngle + Math.PI / 2)
        const rectX = -drawW / 2 - expand
        const rectY = -drawH / 2 - expand
        const rectW = drawW + expand * 2
        const rectH = drawH + expand * 2
        const cornerR = Math.min(beadSize * 0.35 + expand, Math.min(rectW, rectH) / 2)
        drawRoundedRect(ctx, rectX, rectY, rectW, rectH, cornerR)
        ctx.restore()
      }
    }
    /* ----------------------------------------------------- */

    // 绘制任务（图片本身）
    drawTasks.push({
      type: 'bead',
      bead,
      x,
      y,
      beadSize,
      index: info.index,
      centerAngle: info.centerAngle,
    })
  })

  if (drawTasks.length > 0) {
    executeDrawTasks(drawTasks)
  }
}

// 位置计算函数
const recalculateBeadPositions = () => {
  if (!canvasEl || !ctx) return

  const logicalWidth = canvasEl.canvas.width / dprScale
  const logicalHeight = canvasEl.canvas.height / dprScale

  const centerX = logicalWidth / 2
  const centerY = logicalHeight / 2

  const size = Math.min(logicalWidth, logicalHeight)
  const referenceRadius = size * 0.25
  const baseBeadSize = referenceRadius * 0.18
  const gapAngle = state.gapAngle

  const beadInfoArray: any[] = []
  const actualBeadCount = state.beads.length
  if (!actualBeadCount) return

  let maxBeadSize = 0
  state.beads.forEach((bead: any) => {
    const sizeRatio = bead && bead.size ? bead.size / 12 : 1
    const bSize = baseBeadSize * sizeRatio
    maxBeadSize = Math.max(maxBeadSize, bSize)
  })

  const calcTotalAngle = (r: number) => {
    let sum = 0
    state.beads.forEach((bead: any) => {
      const sizeRatio = bead && bead.size ? bead.size / 12 : 1
      const bSize = baseBeadSize * sizeRatio
      const ratio = Math.min(bSize / r, 1)
      sum += 2 * Math.asin(ratio) + gapAngle
    })
    return sum
  }

  // 二分搜索适当半径
  let low = maxBeadSize * 1.05
  let high = referenceRadius * 2
  let radius = referenceRadius
  for (let i = 0; i < 20; i++) {
    const total = calcTotalAngle(radius)
    if (Math.abs(total - 2 * Math.PI) < 1e-3) break
    if (total > 2 * Math.PI) low = radius
    else high = radius
    radius = (low + high) / 2
  }

  // 逐个分配角度并计算坐标
  let currentAngle = -Math.PI / 2
  state.beads.forEach((bead: any, index: number) => {
    const sizeRatio = bead && bead.size ? bead.size / 12 : 1
    const bSize = baseBeadSize * sizeRatio
    const ratio = Math.min(bSize / radius, 1)
    const beadAngle = 2 * Math.asin(ratio)

    const centerAngle = currentAngle + beadAngle / 2
    const x = centerX + radius * Math.cos(centerAngle)
    const y = centerY + radius * Math.sin(centerAngle)

    beadInfoArray.push({
      index,
      beadSize: bSize,
      centerX: x,
      centerY: y,
      centerAngle,
    })

    currentAngle += beadAngle + gapAngle
  })

  // 若计算得到的 radius 大于 referenceRadius，按比例整体缩放珠子尺寸，保持预览手串大小一致
  if (radius > referenceRadius) {
    const scaleRatio = referenceRadius / radius
    radius = referenceRadius
    beadInfoArray.forEach((info) => {
      info.beadSize *= scaleRatio
    })
  }

  // 计算缩放后的总角度，若仍 > 2π，继续按比例收缩珠子，避免首尾重叠
  const calcBeadAngle = (size: number) =>
    size >= radius ? Math.PI / 2 : 2 * Math.asin(Math.min(Math.max(size / radius, -1), 1))

  let totalAngleAfterScale = 0
  beadInfoArray.forEach((info) => {
    totalAngleAfterScale += calcBeadAngle(info.beadSize) + gapAngle
  })

  if (totalAngleAfterScale > 2 * Math.PI + 0.0001) {
    const shrinkRatio = (2 * Math.PI) / totalAngleAfterScale
    beadInfoArray.forEach((info) => {
      info.beadSize *= shrinkRatio
    })
  }

  // 重新根据最新 beadSize 计算角度及位置
  currentAngle = -Math.PI / 2
  beadInfoArray.forEach((info) => {
    const beadAngleNow = calcBeadAngle(info.beadSize)
    const beadCenterAngle = currentAngle + beadAngleNow / 2
    info.centerAngle = beadCenterAngle
    info.beadAngle = beadAngleNow
    info.centerX = centerX + radius * Math.cos(beadCenterAngle)
    info.centerY = centerY + radius * Math.sin(beadCenterAngle)
    currentAngle += beadAngleNow + gapAngle
  })
  /* -------- 校正逻辑结束 -------- */

  state.beadInfoArray = beadInfoArray
  state.actualRadius = radius
}

// 执行绘制任务
const executeDrawTasks = (drawTasks: any[]) => {
  if (state.isProcessingDrawTasks) return

  state.isProcessingDrawTasks = true

  let tasksCompleted = 0
  const totalTasks = drawTasks.length

  const taskCompleted = () => {
    tasksCompleted++
    if (tasksCompleted >= totalTasks) {
      state.isProcessingDrawTasks = false
    }
  }

  drawTasks.forEach((task) => {
    if (task.type === 'bead') {
      const url = task.bead.imagePath || task.bead.image
      if (url) {
        const cacheKey = `${url}&uid=${task.bead.uniqueId || ''}&idx=${task.index}`
        if (beadImageCache[cacheKey]) {
          drawImageWithClipping(
            beadImageCache[cacheKey],
            task.x,
            task.y,
            task.beadSize,
            task.centerAngle + Math.PI / 2,
          )
          taskCompleted()
        } else {
          loadBeadImage(cacheKey, (img) => {
            if (img) {
              drawImageWithClipping(
                img,
                task.x,
                task.y,
                task.beadSize,
                task.centerAngle + Math.PI / 2,
              )
            } else {
              drawColorCircle(task.x, task.y, task.beadSize, '#FFFFFF')
            }
            taskCompleted()
          })
        }
      } else {
        drawColorCircle(task.x, task.y, task.beadSize, '#FFFFFF')
        taskCompleted()
      }
    } else {
      taskCompleted()
    }
  })
}

// Canvas 初始化
const initCanvas = () => {
  console.log('开始初始化Canvas')

  setTimeout(() => {
    try {
      const systemInfo = uni.getSystemInfoSync()
      const screenWidth = systemInfo.screenWidth
      const availableHeight = 400 // 固定高度

      console.log('系统信息:', {
        screenWidth,
        screenHeight: systemInfo.screenHeight,
        availableHeight,
        pixelRatio: systemInfo.pixelRatio,
      })

      state.transformOrigin = {
        x: screenWidth / 2,
        y: availableHeight / 2,
      }

      const query = uni.createSelectorQuery()
      query
        .select('#previewCanvas')
        .fields({ node: true, size: true }, (res: any) => {
          console.log('Canvas查询结果:', res)
          if (res && res.node) {
            const canvas = res.node
            canvasEl = { canvas, node: canvas }
            ctx = canvas.getContext('2d')

            // 依据设备像素比提高分辨率，防止珠子图片出现锯齿
            const dpr = systemInfo.pixelRatio || 1
            dprScale = dpr
            canvas.width = screenWidth * dpr
            canvas.height = availableHeight * dpr
            if ((canvas as any).style) {
              ;(canvas as any).style.width = `${screenWidth}px`
              ;(canvas as any).style.height = `${availableHeight}px`
            }

            // 按像素比缩放坐标系，并开启高质量平滑
            ctx.scale(dpr, dpr)
            ;(ctx as any).imageSmoothingEnabled = true
            ;(ctx as any).imageSmoothingQuality = 'high'

            if (!ctx) {
              console.error('无法获取Canvas 2D上下文')
              return
            }

            console.log('Canvas初始化成功', {
              width: canvas.width,
              height: canvas.height,
              screenWidth,
              availableHeight,
              pixelRatio: systemInfo.pixelRatio,
            })

            state.scale = 1
            state.rotation = 0
            initPreviewBeads()
            state.canvasReady = true
            console.log('开始绘制手串，珠子数量:', state.beads.length, state.beads)

            // 先绘制一个测试圆圈
            ctx.fillStyle = 'red'
            ctx.beginPath()
            ctx.arc(canvas.width / 2, canvas.height / 2, 50, 0, Math.PI * 2)
            ctx.fill()
            console.log('测试圆圈已绘制，位置:', canvas.width / 2, canvas.height / 2)

            // 绘制一个绿色边框来显示Canvas边界
            // ctx.strokeStyle = 'green'
            // ctx.lineWidth = 2
            // ctx.strokeRect(0, 0, canvas.width, canvas.height)
            // console.log('Canvas边界已绘制')

            drawBracelet()
          } else {
            console.error('无法获取Canvas节点', res)
          }
        })
        .exec()
    } catch (error) {
      console.error('Canvas初始化出错:', error)
      canvasEl = null
      ctx = null
      state.canvasReady = false
    }
  }, 500) // 延迟500ms确保DOM已渲染
}
// 组件卸载时清理
onBeforeUnmount(() => {
  if (state.animationTimer) {
    clearTimeout(state.animationTimer)
  }
  if (state.beadDrawTaskTimer) {
    clearTimeout(state.beadDrawTaskTimer)
  }
})

const CustomerServiceElement = ref(null)

// 显示消息框
function showMessageBox() {
  if (CustomerServiceElement.value) {
    CustomerServiceElement.value.open()
  }
}
</script>

<style lang="scss" scoped>
.body {
  width: 100vw;
  height: 100vh;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

// Canvas 容器样式
.canvas-container {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 40vh;
  max-height: 40vh;
  padding-bottom: 0;
  overflow: hidden;
  background: transparent;
  // margin-top: 80rpx;
}

// 网格背景
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image: linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.1;
}

// Canvas 样式
:deep(.bracelet-canvas) {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 400px;
  touch-action: none;
  background: transparent;
}
.itemBottomBorderClass {
  height: 82rpx;
  border-bottom: 1rpx solid #e8e8e8;
}

//商品底部按钮开始位置
.productDetailsButtonBodyPlaceholderClass {
  height: 188rpx;
}
.productDetailsButtonBodyClass {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  height: 188rpx;
  background: #fff;
  .productDetailsButtonBodyInnerClass {
    display: flex;
    width: 100%;
    height: 100%;
    margin-top: 20rpx;
    .productDetailsButtonBodyInnerLeftClass {
      display: flex;
      flex: 1;
      justify-content: center; // 水平居中
      :deep() {
        //按钮样式
        .custom1 {
          width: 90%;
          height: 81rpx;
        }
        //按钮圆角
        .wd-button.is-round {
          border-radius: 13rpx;
        }
        //按钮文字样式
        .wd-button__text {
          font-size: 30rpx;
          font-weight: bold;
        }
      }
    }
    .productDetailsButtonBodyInnerRightClass {
      display: flex;
      flex: 1;
      justify-content: center; // 水平居中
      :deep() {
        //按钮样式
        .custom2 {
          width: 90%;
          height: 81rpx;
          background: #e73c3c;
        }
        //按钮圆角
        .wd-button.is-round {
          border-radius: 13rpx;
        }
        //按钮文字样式
        .wd-button__text {
          font-size: 30rpx;
          font-weight: bold;
        }
      }
    }
  }
  //商品底部按钮结束位置
}
</style>
