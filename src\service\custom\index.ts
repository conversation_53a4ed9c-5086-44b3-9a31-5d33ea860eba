import { http } from '@/utils/http'

// 宝石标签类型
export interface IGemTypeListItem {
  id: string
  name: string
  list: {
    id: string
    name: string
    picture: string
  }[]
}

/** GET 请求 获取宝石列表 */
export const gemTypeList = (type) => {
  return http.get<IGemTypeListItem[]>(`/gem/list`, { type })
}

/** POST 请求 新增设计列表 */
export const designAdd = (data) => {
  return http.post<void>(`/design/add`, data)
}

/** GET 请求 获取设计详情 */
export const getDesignDetail = (id: string) => {
  return http.get<void>(`/design/detail/${id}`)
}
