/*
 * @Author: weish<PERSON>
 * @Date: 2024-01-25 23:06:48
 * @LastEditTime: 2024-01-26 14:00:48
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: \wot-design-uni\src\uni_modules\wot-design-uni\components\composables\useTranslate.ts
 * 记得注释
 */
import zhCN from 'wot-design-uni/locale/lang/zh-CN'
import { camelCase, getPropByPath, isFunction } from '../common/util'

export const useTranslate = (name?: string) => {
  const prefix = name ? camelCase(name) + '.' : ''
  const translate = (key: string, ...args: unknown[]) => {
    const currentMessages = zhCN
    const message = getPropByPath(currentMessages, prefix + key)
    return isFunction(message) ? message(...args) : message
  }

  return { translate }
}
