<template>
  <view class="tab-wrapper" :class="customClass">
    <view
      v-for="(item, index) in tabs"
      :key="index"
      :id="`tab-${index}`"
      class="text-22rpx fw-bold whitespace-nowrap tab-item"
      :class="{ active: activeIndex === index }"
    >
      {{ item }}
    </view>

    <!-- 底部滑块 -->
    <image
      class="tab-indicator"
      :style="{ left: `${indicatorLeft}rpx` }"
      :src="images"
      mode="widthFix"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, nextTick, getCurrentInstance } from 'vue'

defineOptions({ name: 'TabSelector' })
// prop基本属性
const props = defineProps({
  customClass: {
    type: String,
    default: '',
  },
  images: {
    type: String,
    default: '../static/images/hkImages.png',
  },
  tabs: {
    type: Array as () => string[],
    required: true,
    default: () => [],
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
  max: {
    type: Number,
    default: 3,
  },
  offset: {
    type: Number,
    default: 0,
  },
})
// 事件：子组件向父组件传递 change 事件
const emit = defineEmits(['change'])
// tabs 数据
const tabs = ref<string[]>(props.tabs)
// 当前激活 tab 索引
const activeIndex = ref<number>(props.activeIndex)
// 滑块图片路径
const images = ref<string>(props.images)

// 滑块 indicator 的 left 位置
const indicatorLeft = ref(0)
// 滑块宽度（单位：rpx）
const indicatorWidth = 50
// 单个 tab 的宽度（单位：rpx）
let tabWidth = 140
// 如果 tab 数量小于等于 3，平均分配宽度
if (tabs.value.length <= props.max && tabs.value.length > 0) {
  tabWidth = (750 - props.offset) / tabs.value.length
}

// 更新滑块位置
function updateIndicatorPosition(index: number) {
  indicatorLeft.value = index * tabWidth + (tabWidth - indicatorWidth) / 2
}
// 滑块chang事件
// function changeTab(index: number) {
//   if (activeIndex.value !== index) {
//     activeIndex.value = index
//     updateIndicatorPosition(index)
//     emit('change', { value: index })
//   }
// }

onMounted(() => {
  updateIndicatorPosition(activeIndex.value)
})

watch(
  () => props.activeIndex,
  (newVal) => {
    activeIndex.value = newVal
    updateIndicatorPosition(newVal)
  },
)
</script>

<style lang="scss" scoped>
:deep() ::-webkit-scrollbar {
  display: block;
  width: 0 !important;
  height: 0 !important;
  overflow: auto !important ;
  -webkit-appearance: auto !important;
  background: transparent;
}

.tab-wrapper {
  position: relative;
  display: flex;
  padding-top: 30rpx;
  border-top: 2rpx solid #e6bfbf;
}

.tab-container {
  position: relative;
  display: flex;
  white-space: nowrap;
}

.tab-item {
  position: relative;
  height: 132rpx;
  padding: 11rpx 19rpx 11rpx 19rpx;
  margin: auto;
  color: #a23d3d;
  text-align-last: justify;
  background: radial-gradient(
      var(--border_radius) at var(--border_radius) var(--border_radius),
      transparent calc(97% - var(--border_width)),
      var(--color) calc(100% - var(--border_width)) 98%,
      transparent
    ),
    linear-gradient(var(--color), var(--color)), linear-gradient(var(--color), var(--color)),
    linear-gradient(var(--color), var(--color)), linear-gradient(var(--color), var(--color));
  background-repeat: repeat, no-repeat, no-repeat, no-repeat, no-repeat;
  background-position:
    calc(-1 * var(--border_radius)) calc(-1 * var(--border_radius)),
    calc(var(--border_radius) - 1px) 0,
    calc(var(--border_radius) - 1px) 100%,
    // 两条横边
    0 calc(var(--border_radius) - 1px),
    100% calc(var(--border_radius) - 1px); // 两条纵边
  background-size:
    100% 100%,
    calc(100% - calc(var(--border_radius) * 2 - 2px)) var(--border_width),
    calc(100% - calc(var(--border_radius) * 2 - 2px)) var(--border_width),
    var(--border_width) calc(100% - calc(var(--border_radius) * 2 - 2px)),
    var(--border_width) calc(100% - calc(var(--border_radius) * 2 - 2px));
  writing-mode: vertical-rl; /* 垂直从右到左排列文字 */
  text-orientation: upright; /* 保持文字直立，不旋转 */
  --color: #ffbebe;
  --border_radius: 10rpx;
  --border_width: 1px;
}

.active {
  position: relative;
  height: 132rpx;
  padding: 11rpx 19rpx 11rpx 19rpx;
  margin: auto;
  color: #c5171f;
  text-align-last: justify;
  background: radial-gradient(
      var(--border_radius) at var(--border_radius) var(--border_radius),
      transparent calc(97% - var(--border_width)),
      var(--color) calc(100% - var(--border_width)) 98%,
      transparent
    ),
    linear-gradient(var(--color), var(--color)), linear-gradient(var(--color), var(--color)),
    linear-gradient(var(--color), var(--color)), linear-gradient(var(--color), var(--color));
  background-repeat: repeat, no-repeat, no-repeat, no-repeat, no-repeat;
  background-position:
    calc(-1 * var(--border_radius)) calc(-1 * var(--border_radius)),
    calc(var(--border_radius) - 1px) 0,
    calc(var(--border_radius) - 1px) 100%,
    // 两条横边
    0 calc(var(--border_radius) - 1px),
    100% calc(var(--border_radius) - 1px); // 两条纵边
  background-size:
    100% 100%,
    calc(100% - calc(var(--border_radius) * 2 - 2px)) var(--border_width),
    calc(100% - calc(var(--border_radius) * 2 - 2px)) var(--border_width),
    var(--border_width) calc(100% - calc(var(--border_radius) * 2 - 2px)),
    var(--border_width) calc(100% - calc(var(--border_radius) * 2 - 2px));
  writing-mode: vertical-rl; /* 垂直从右到左排列文字 */
  text-orientation: upright; /* 保持文字直立，不旋转 */
  --color: #c5171f;
  --border_radius: 10rpx;
  --border_width: 1px;
}

.tab-indicator {
  position: absolute;
  top: -26rpx;
  width: 50rpx;
  height: 50rpx;
  transition: left 0.3s;
}
</style>
