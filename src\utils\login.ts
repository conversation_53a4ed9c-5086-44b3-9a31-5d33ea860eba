import { login, ILoginItem, phone } from '@/service/login'
import { userInfo, IUserInfoItem } from '@/service/myArchive/index'
import { useUserStore } from '@/store'
const userStore = useUserStore()

// 获取用户手机号码
export function getPhone(code: string): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const { data, run } = useRequest<boolean>(() => phone(code))
    run()
      .then(() => {
        if (data.value) {
          resolve(data.value)
        } else {
          reject(data.value)
        }
      })
      .catch(reject)
  })
}

// 登录并获取用户基础信息
export function loginRun(): Promise<ILoginItem> {
  return new Promise((resolve, reject) => {
    if (userStore.isLogined) {
      return resolve(userStore.userInfo as ILoginItem)
    }

    uni.login({
      provider: 'weixin',
      success({ code }) {
        const { data, run } = useRequest<ILoginItem>(() => login(code))
        run()
          .then(() => {
            const userInfo = data.value
            if (userInfo) {
              userStore.setUserInfo(userInfo)
              resolve(userInfo)
            } else {
              reject(new Error('登录数据为空'))
            }
          })
          .catch(reject)
      },
      fail(err) {
        reject(err)
      },
    })
  })
}

// 获取用户基础信息
export function getUserInfo(): Promise<IUserInfoItem> {
  return new Promise((resolve, reject) => {
    const { data, run } = useRequest<IUserInfoItem>(() => userInfo())
    run()
      .then(() => {
        const { phone, nickName, avatar, gender } = data.value
        const userInfo = { phone, nickName, avatar, gender }
        if (userInfo) {
          userStore.setUserInfo(userInfo)
          resolve(data.value)
        } else {
          reject(new Error('获取用户信息失败'))
        }
      })
      .catch(reject)
  })
}
