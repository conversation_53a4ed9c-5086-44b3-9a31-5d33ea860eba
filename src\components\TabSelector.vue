<template>
  <view class="tab-wrapper" :class="customClass">
    <scroll-view
      id="tab-scroll"
      scroll-x
      scroll-with-animation
      class="tab-container"
      :scroll-left="scrollLeft"
    >
      <view
        v-for="(item, index) in tabs"
        :key="item.id"
        class="tab-item"
        :class="{ active: activeIndex === index }"
        :style="{ minWidth: `${tabWidth}rpx` }"
        @click="changeTab(item, index)"
      >
        <text class="tab-text">{{ item.name }}</text>
      </view>

      <!-- 底部滑块 -->
      <image
        class="tab-indicator"
        :style="{ left: `${indicatorLeft}rpx` }"
        :src="images"
        mode="widthFix"
      />
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, nextTick, computed, getCurrentInstance } from 'vue'
import { ITabsListItem } from '@/service/index/index'

// ---------- Props & Emit ----------
defineOptions({ name: 'TabSelector' })

const props = defineProps({
  customClass: { type: String, default: '' },
  images: { type: String, default: '../static/images/tab-indicator.png' },
  tabs: {
    type: Array as () => ITabsListItem[],
    required: true,
    default: () => [],
  },
  activeIndex: { type: Number, default: 0 },
  max: { type: Number, default: 3 },
})

const emit = defineEmits(['change'])

// ---------- Refs & Reactive ----------
const activeIndex = ref<number>(props.activeIndex)
const scrollLeft = ref(0)
const indicatorLeft = ref(0)
const indicatorWidth = 113

const { proxy } = getCurrentInstance()!

// ---------- Computed ----------
// 响应式计算 tab 宽度
const tabWidth = computed(() => {
  const len = props.tabs.length
  const max = props.max || 4
  return len > 0 && len <= max ? 750 / len : 200
})

// ---------- Layout Helpers ----------
// 获取 DOM rect 信息
function getRect(selector: string, all = false): Promise<UniApp.NodeInfo | UniApp.NodeInfo[]> {
  return new Promise((resolve) => {
    const query = uni.createSelectorQuery().in(proxy)
    all
      ? query.selectAll(selector).boundingClientRect()
      : query.select(selector).boundingClientRect()
    query.exec((res) => resolve(res[0]))
  })
}

// 滚动让当前 tab 居中
function scrollIntoCenter() {
  if (!props.tabs.length) return
  nextTick(() => {
    Promise.all([
      getRect('.tab-item', true) as Promise<UniApp.NodeInfo[]>,
      getRect('#tab-scroll') as Promise<UniApp.NodeInfo>,
    ]).then(([itemRects, scrollRect]) => {
      const index = activeIndex.value
      const selected = itemRects[index]
      const offsetLeft = itemRects.slice(0, index).reduce((sum, item) => sum + item.width, 0)
      const left = offsetLeft - (scrollRect.width - selected.width) / 2

      scrollLeft.value = Math.abs(scrollLeft.value - left) < 1 ? left + Math.random() / 10000 : left
    })
  })
}

// 更新滑块位置
function updateIndicatorPosition(index: number) {
  indicatorLeft.value = index * tabWidth.value + (tabWidth.value - indicatorWidth) / 2
  scrollIntoCenter()
}

// 切换 tab
function changeTab(item: ITabsListItem, index: number) {
  if (activeIndex.value !== index) {
    activeIndex.value = index
    updateIndicatorPosition(index)
    emit('change', item, index)
  }
}

// ---------- Watchers ----------

// 监听父组件传入的 activeIndex 更新
watch(
  () => props.activeIndex,
  (val) => {
    activeIndex.value = val
    updateIndicatorPosition(val)
  },
)

// 监听 tabs 更新（支持异步加载）
watch(
  () => props.tabs,
  (val) => {
    if (val.length > 0) {
      nextTick(() => updateIndicatorPosition(activeIndex.value))
    }
  },
  { immediate: true },
)

// ---------- Init ----------
onMounted(() => {
  updateIndicatorPosition(activeIndex.value)
})
</script>

<style lang="scss" scoped>
:deep() ::-webkit-scrollbar {
  display: block;
  width: 0 !important;
  height: 0 !important;
  overflow: auto !important ;
  -webkit-appearance: auto !important;
  background: transparent;
}

.tab-wrapper {
  position: relative;
  overflow: hidden;
}

.tab-container {
  position: relative;
  display: flex;
  white-space: nowrap;
}

.tab-item {
  display: inline-block;
  min-width: 200rpx;
  text-align: center;
}

.tab-text {
  font-size: 32rpx;
  line-height: 100rpx;
  color: #333;
}

.tab-item.active .tab-text {
  font-weight: bold;
  color: #df2227;
}

.tab-indicator {
  position: absolute;
  top: 80rpx;
  width: 113rpx;
  height: 18rpx;
  transition: left 0.3s;
}
</style>
