<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <!-- 海报展示区开始位置 -->
  <!-- <view class="posterDisplayClass">
    <image
      src="https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/84747fc8b369459e99e2826937cd0886_mergeImage.png"
      mode="widthFix"
    />
    <view class="textPalyClass">
      <view class="textTitleClass">高端珠宝定制</view>
      <image class="iconClass" src="../../static/customizedJewelry/icon.png" mode="widthFix" />
    </view>
  </view> -->
  <!-- 海报展示区结束位置 -->

  <!-- 服务亮点开始位置 -->
  <!-- <view class="serviceHighlightClass">
    <view class="serviceHighlightTitleClass">服务亮点</view>
    <view class="serviceHighlightTypeClass">
      <image src="../../static/customizedJewelry/fwldType.png" mode="widthFix" />
    </view>
    <view class="m-8rpx">
      <wd-row>
        <wd-col :span="8" v-for="item in listData" :key="item.id">
          <view class="serviceHighlightTypeItemClass">
            <view class="serviceHighlightTypeItemTitleClass">{{ item.title }}</view>
            <view class="serviceHighlightTypeItemTextClass">{{ item.text }}</view>
          </view>
        </wd-col>
      </wd-row>
    </view>
  </view> -->
  <!-- 服务亮点结束位置 -->

  <!-- 适用场景开始位置 -->
  <!-- <view class="applicableScenariosClass">
    <view class="applicableScenariosTitleClass">适用场景</view>
    <view class="m-9rpx">
      <wd-row>
        <wd-col :span="12" v-for="item in itemData" :key="item.id">
          <view class="applicableScenariosItemClass">
            <image :src="item.url" class="applicableScenariosItemImageClass" mode="aspectFill" />
            <view class="applicableScenariosItemTextClass">{{ item.content }}</view>
          </view>
        </wd-col>
      </wd-row>
    </view>
  </view> -->
  <!-- 适用场景结束位置 -->
  <web-view v-if="curbannerData?.formatType === 2" :src="curbannerData?.content"></web-view>
  <mp-html v-else :content="curbannerData?.content" />
</template>

<script lang="ts" setup>
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
defineOptions({
  name: 'customizedJewelry',
})

const { getData } = useData()
const curbannerData = getData('curbannerData')

// // 服务亮点展示内容
// const listData = ref([
//   {
//     id: 1,
//     title: '私人定制',
//     text: '一对一分析，贴合个人命盘定制方案',
//   },
//   {
//     id: 2,
//     title: '科学赋能',
//     text: '融合大数据与传统文化，拒绝空泛玄学',
//   },
//   {
//     id: 3,
//     title: '实用导向',
//     text: '聚焦现实问题，提供可落地的改善建议',
//   },
// ])

// const itemData = ref([
//   {
//     id: 1,
//     content: '事业瓶颈期寻求突破方向',
//     url: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/22a853e66608462682acebe534cf65f8_mergeImage.png',
//   },
//   {
//     id: 2,
//     content: '婚姻情感中预判关系走势',
//     url: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/22a853e66608462682acebe534cf65f8_mergeImage.png',
//   },
//   {
//     id: 3,
//     content: '投资决策前洞察风险机遇',
//     url: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/22a853e66608462682acebe534cf65f8_mergeImage.png',
//   },
//   {
//     id: 4,
//     content: '人生关键节点(如创业、移民、生育)的吉时规划',
//     url: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/3a5c12f52cc44d0995e090fcb5b51ee0_mergeImage.png',
//   },
// ])
</script>

<style lang="scss" scoped>
// 海报展示区css开始位置
// .posterDisplayClass {
//   position: relative;
//   height: 330rpx;
//   .textPalyClass {
//     position: absolute;
//     top: 50%;
//     left: 50%;
//     width: 300rpx;
//     height: 90rpx;
//     transform: translate(-50%, -50%);
//     .textTitleClass {
//       font-family: SourceHanSerifCN;
//       font-size: 40rpx;
//       font-style: normal;
//       font-weight: bold;
//       line-height: 57rpx;
//       color: #ffffff;
//       text-align: center;
//       letter-spacing: 10rpx;
//     }
//     .iconClass {
//       display: block;
//       width: 170rpx;
//       height: 25rpx;
//       margin: 0 auto;
//     }
//   }
// }
// // 海报展示区css结束位置
// // 项目亮点css开始位置
// .serviceHighlightClass {
//   height: 421rpx;
//   padding-top: 18rpx;
//   margin: 20rpx 25rpx 0 25rpx;
//   background: #fff;
//   border-radius: 20rpx;
//   .serviceHighlightTitleClass {
//     width: 168rpx;
//     height: 70rpx;
//     margin: 0 auto;
//     font-family: SourceHanSerifCN;
//     font-size: 27rpx;
//     font-style: normal;
//     font-weight: bold;
//     line-height: 70rpx;
//     color: #fff9f3;
//     text-align: center;
//     background-image: url('../../static/images/buttonbg.png');
//     background-repeat: no-repeat;
//     background-position: center;
//     background-size: contain;
//   }
//   .serviceHighlightTypeClass {
//     width: 620rpx;
//     height: 151rpx;
//     margin: 20rpx auto 20rpx auto;
//   }
//   .serviceHighlightTypeItemClass {
//     height: 104rpx;
//     text-align: center;
//     .serviceHighlightTypeItemTitleClass {
//       font-family: SourceHanSerifCN;
//       font-size: 27rpx;
//       font-style: normal;
//       font-weight: bold;
//       line-height: 39rpx;
//       color: #000000;
//     }
//     .serviceHighlightTypeItemTextClass {
//       font-family: SourceHanSerifCN;
//       font-size: 22rpx;
//       font-style: normal;
//       font-weight: 500;
//       line-height: 30rpx;
//       color: #272d38;
//       text-align: center;
//     }
//   }
// }
// // 项目亮点css结束位置
// // 适用场景css开始位置
// .applicableScenariosClass {
//   padding-top: 18rpx;
//   margin: 20rpx 25rpx 40rpx 25rpx;
//   background: #fff;
//   border-radius: 20rpx;
//   .applicableScenariosTitleClass {
//     width: 168rpx;
//     height: 70rpx;
//     margin: 0 auto;
//     font-family: SourceHanSerifCN;
//     font-size: 27rpx;
//     font-style: normal;
//     font-weight: bold;
//     line-height: 70rpx;
//     color: #fff9f3;
//     text-align: center;
//     background-image: url('../../static/images/buttonbg.png');
//     background-repeat: no-repeat;
//     background-position: center;
//     background-size: contain;
//   }
// }

// .applicableScenariosItemClass {
//   margin: 36rpx;
//   .applicableScenariosItemImageClass {
//     height: 270rpx;
//     background: #f8eded;
//     border-radius: 20rpx;
//   }
//   .applicableScenariosItemTextClass {
//     margin-top: 19rpx;
//     font-family: SourceHanSerifCN;
//     font-size: 22rpx;
//     font-style: normal;
//     font-weight: 500;
//     line-height: 30rpx;
//     color: #272d38;
//     text-align: center;
//   }
// }
// 适用场景css结束位置
</style>
