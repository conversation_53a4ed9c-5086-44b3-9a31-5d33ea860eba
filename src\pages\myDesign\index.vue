<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的设计',
  },
}
</route>

<template>
  <view v-if="total">
    <view
      class="myDesignItemBodyClass"
      v-for="item of designData"
      :key="item.id"
      @click="goToCustomizationDetails(item.id)"
    >
      <view class="myDesignItemBodyImageClass">
        <image :src="item.designPicture" mode="aspectFill" />
      </view>
      <view class="myDesignItemBodyInnerClass">
        <view class="myDesignItemBodyInnerTimeClass">{{ item.createTime }}</view>
        <view class="myDesignItemBodyInnerPriceClass">¥{{ item.price }}</view>
        <view class="myDesignItemBodyInnerButtonClass">
          <wd-button custom-class="custom" plain @tap.prevent="showMessageBox(item.id)">
            删除
          </wd-button>
        </view>
      </view>
    </view>
    <wd-loadmore
      :state="state"
      @reload="loadmore"
      :loading-props="{ color: '#e73c3c', size: 20 }"
    />
  </view>
  <EmptyState v-else>
    <view class="lh-39rpx text-27rpx fw-bold font-[SourceHanSerifCN] c-#cac8c8 text-center">
      暂无设计
    </view>
    <view class="text-center mt-20rpx">
      <wd-button type="error" :round="false" size="small" @click="goToDiy">去设计</wd-button>
    </view>
  </EmptyState>
  <MessageBoxHb
    ref="MessageBoxHbElement"
    msg="确定要删除吗？"
    cancelButtonText="我再想想"
    @confirm="confirm"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import MessageBoxHb from '@/components/MessageBoxHb.vue'
import EmptyState from '@/components/EmptyState.vue'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { IDesignPageItem, designPage, IDesignQuery, designDelete } from '@/service/myDesign/index'

const designData = ref([])
const pages = ref<number>(1)
const state = ref<LoadMoreState>('finished')
const total = ref(0)
const activeId = ref(null)
// 引入消息框组件
const MessageBoxHbElement = ref<InstanceType<typeof MessageBoxHb> | null>(null)
// 显示消息框
function showMessageBox(id: string) {
  activeId.value = id
  if (MessageBoxHbElement.value) {
    MessageBoxHbElement.value.open()
  }
}
// 确认删除
function confirm() {
  const { data, run } = useRequest<boolean>(() => designDelete(activeId.value))
  run().then(() => {
    if (data.value) {
      uni.showToast({ icon: 'none', title: '删除成功' })
      designData.value = designData.value.filter((item) => item.id !== activeId.value)
      total.value = designData.value.length
    }
  })
}
// 跳转定制详情页面
function goToCustomizationDetails(id: number) {
  // 这里可以传递id参数到定制详情页面
  uni.navigateTo({
    url: `/pages/customizationDetails/index?id=${id}&isEdit=1`,
  })
}

// 跳转diy
function goToDiy() {
  uni.reLaunch({ url: '/pages/diy/diy' })
}

// 页面加载时加载数据
onReachBottom(() => {
  if (designData.value.length < total.value) {
    loadmore()
  } else {
    state.value = 'finished'
  }
})

// 加载更多数据
async function loadmore() {
  await getDesignPage()
  if (designData.value.length >= total.value) {
    state.value = 'finished'
  } else {
    state.value = 'loading'
  }
}

// 获取订单信息
async function getDesignPage() {
  const parameter: IDesignQuery = { page: pages.value, size: 10 }
  const { data, run } = useRequest<IDesignPageItem>(() => designPage(parameter))
  run().then(() => {
    pages.value++

    designData.value = [...designData.value, ...data.value.list]
    total.value = data.value.total
  })
}

onShow(() => {
  pages.value = 1
  designData.value = []
  getDesignPage()
})
</script>

<style lang="scss" scoped>
.myDesignItemBodyClass {
  display: flex;
  height: 220rpx;
  padding: 20rpx;
  margin: 25rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .myDesignItemBodyImageClass {
    width: 220rpx;
    height: 220rpx;
    background: #ffffff;
    border-radius: 15rpx;
  }
  .myDesignItemBodyInnerClass {
    position: relative;
    flex: 1;
    padding-left: 20rpx;
    .myDesignItemBodyInnerTimeClass {
      margin-top: 8rpx;
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 30rpx;
      color: #1e2127;
    }
    .myDesignItemBodyInnerPriceClass {
      margin-top: 20rpx;
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 30rpx;
      color: #1e2127;
    }
    .myDesignItemBodyInnerButtonClass {
      position: absolute;
      right: 0;
      bottom: 0;
      :deep() {
        //按钮样式
        .custom {
          width: 135rpx;
          height: 56rpx;
        }
        //按钮圆角
        .wd-button.is-round {
          min-width: 135rpx;
          border-radius: 11rpx;
        }
        //按钮文字样式
        .wd-button__text {
          font-family: SourceHanSerifCN;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 37rpx;
          color: #f32927;
        }
      }
    }
  }
}
</style>
