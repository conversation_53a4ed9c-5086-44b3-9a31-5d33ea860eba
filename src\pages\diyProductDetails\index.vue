<route lang="json5">
{
  style: {
    navigationBarTitleText: '商品详情',
  },
}
</route>

<template>
  <view class="productDetailsImagePalyClass">
    <image :src="productDetailsData?.picture" mode="aspectFill" />
  </view>
  <view class="productDetailsContentClass">
    <view class="goodsTextClass">
      <wd-row>
        <wd-col :span="4">
          <view class="goodsPriceClass">
            ¥
            <text class="text-40rpx">{{ productDetailsData?.price }}</text>
          </view>
        </wd-col>
        <wd-col :span="20">
          <view class="flex justify-end flex-wrap mt-4rpx">
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('健康')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #027741">健康</view>
            </view>
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('财富')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #e96f00">财富</view>
            </view>
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('爱情')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #fd466f">爱情</view>
            </view>
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('事业')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #6b4023">事业</view>
            </view>
            <view class="goodsTypeClass ml-5rpx" v-if="productDetailsData?.labels.includes('平安')">
              <image
                src="../../static/home/<USER>"
                class="goodsTypeImagesClass"
                mode="aspectFill"
              />
              <view class="goodsTypeTextClass" style="color: #e32117">平安</view>
            </view>
          </view>
        </wd-col>
      </wd-row>
    </view>
    <view class="productDetailsTitleClass">{{ productDetailsData?.name }}</view>
  </view>
  <view class="productDetailsTextClass">
    <view class="productDetailsTextTitleClass">详细介绍</view>
    <view class="productDetailsTextContentClass">
      <mp-html :content="productDetailsData?.content" />
    </view>
  </view>
  <view class="productDetailsButtonBodyPlaceholderClass"></view>
  <view class="productDetailsButtonBodyClass">
    <view class="productDetailsButtonBodyInnerClass">
      <button class="productDetailsButtonBodyInnerLeftClass" open-type="share">
        <view class="productDetailsButtonBodyInnerLeftShareClass">
          <image src="../../static/images/shareIcon.png" mode="aspectFill" />
          <view class="productDetailsButtonBodyInnerLeftShareTextClass">分享</view>
        </view>
      </button>
      <view class="productDetailsButtonBodyInnerRightClass" @click="goToCustomization">
        <wd-button custom-class="custom1">
          <image
            class="diyButtonIconClass pb-8rpx"
            src="../../static/diy/diyxgIcon.png"
            mode="widthFix"
          />
          <text class="pl-20rpx">我要修改</text>
        </wd-button>
      </view>
      <view class="productDetailsButtonBodyInnerRightClass" @click="showMessageBox">
        <wd-button custom-class="custom2">
          <view class="i-carbon-headset pb-8rpx" />
          <text class="pl-20rpx">联系客服</text>
        </wd-button>
      </view>
    </view>
  </view>
  <CustomerService ref="CustomerServiceElement" />
</template>

<script lang="ts" setup>
import { ICustomDetailItem, customDetail } from '@/service/diy/index'
import { onLoad } from '@dcloudio/uni-app'
import CustomerService from '@/components/CustomerService.vue'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import { useLoginGuard } from '@/composables/useTheme'
import { getBannerList } from '@/utils/getBannerList'

const { checkLogin } = useLoginGuard()

defineOptions({
  name: 'productDetails',
})
const productDetailsData = ref<ICustomDetailItem | any>(null)
const CustomerServiceElement = ref(null)

// 默认分享图片
const defaultShareImage = ref('')

// 获取默认分享图
const fetchDefaultShareImage = async () => {
  try {
    const list = await getBannerList('11')
    if (Array.isArray(list) && list.length > 0) {
      defaultShareImage.value = list[0].picture || ''
    }
  } catch (e) {
    console.error('获取默认分享图失败', e)
  }
}

const detailId = ref('')
onLoad((options) => {
  const { id } = options
  detailId.value = id
  getCustomDetail(id)
  // 预取默认分享图
  fetchDefaultShareImage()
})

// 跳转定制页面
function goToCustomization() {
  if (!checkLogin()) return
  const { summaryInfo, beads } = JSON.parse((productDetailsData.value as any).bracelets)
  const { setData } = useData()
  setData('dzxq', {
    beads,
    summaryInfo,
  })
  uni.navigateTo({
    url: `/pages/diy/modules/freeCustomize`,
  })
}

// 分享
onShareAppMessage(() => {
  return {
    title: productDetailsData.value?.name,
    path: `pages/diyProductDetails/index?id=${detailId.value}`,
    imageUrl: defaultShareImage.value || '',
  }
})

function getCustomDetail(id: string) {
  const { data, run } = useRequest<ICustomDetailItem>(() => customDetail(id))
  run().then(() => {
    console.log('data', data.value)
    productDetailsData.value = data.value
  })
}

// 显示消息框
function showMessageBox() {
  if (CustomerServiceElement.value) {
    CustomerServiceElement.value.open()
  }
}
</script>

<style lang="scss" scoped>
//商品图片开始位置
.productDetailsImagePalyClass {
  height: 750rpx;
  background: #f8eded;
}
//商品图片结束位置
//商品价格分类开始位置
.productDetailsContentClass {
  padding-bottom: 20rpx;
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .goodsTextClass {
    padding: 30rpx;
    .goodsPriceClass {
      font-family: SourceHanSerifCN;
      font-size: 26rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 57rpx;
      color: #fc3b3b;
      text-align: left;
    }
    .goodsTypeClass {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 45rpx;
      margin-top: 8rpx;
      background: rgba(117, 117, 117, 0.07);
      border-radius: 23rpx;
      .goodsTypeImagesClass {
        width: 30rpx;
        height: 23rpx;
      }
      .goodsTypeTextClass {
        font-family: SourceHanSerifCN;
        font-size: 22rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 22rpx;
      }
    }
  }
  .productDetailsTitleClass {
    margin: 0 30rpx;
    font-family: SourceHanSerifCN;
    font-size: 30rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 32rpx;
    color: #000000;
    text-align: left;
  }
}
//商品价格分类结束位置
//商品详情开始位置
.productDetailsTextClass {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  .productDetailsTextTitleClass {
    padding: 30rpx;
    font-family: SourceHanSerifCN;
    font-size: 30rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 44rpx;
    color: #000000;
    text-align: left;
  }
  .productDetailsTextContentClass {
    padding: 0 30rpx 30rpx 30rpx;
    font-family: SourceHanSerifCN;
    font-size: 25rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 43rpx;
    color: #000000;
    text-align: left;
  }
}
//商品详情结束位置
//商品底部按钮开始位置
.productDetailsButtonBodyPlaceholderClass {
  height: 188rpx;
}
.productDetailsButtonBodyClass {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  height: 188rpx;
  background: #fff;
  .productDetailsButtonBodyInnerClass {
    display: flex;
    width: 100%;
    height: 100%;
    .productDetailsButtonBodyInnerLeftClass {
      display: flex;
      flex: 2;
      justify-content: center; // 水平居中
      background-color: #fff; // 可选，调试用;
      .productDetailsButtonBodyInnerLeftShareClass {
        width: 44rpx;
        height: 44rpx;
        // margin-top: 29rpx;
      }
      .productDetailsButtonBodyInnerLeftShareTextClass {
        font-family: SourceHanSerifCN;
        font-size: 20rpx;
        font-style: normal;
        font-weight: bold;
        line-height: 0;
        color: #1e2127;
        text-align: right;
      }
    }
    .productDetailsButtonBodyInnerRightClass {
      flex: 4;
      .diyButtonIconClass {
        width: 32rpx;
        height: 32rpx;
      }
      :deep() {
        //按钮样式
        .custom1 {
          width: 270rpx;
          height: 80rpx;
          margin: 20rpx 27rpx 0 0;
          color: #e73c3c;
          background: #ffffff;
          border: 1rpx solid #e73c3c;
        }
        .custom2 {
          width: 270rpx;
          height: 80rpx;
          margin: 20rpx 27rpx 0 0;
          color: #fff9f3;
          background: #e73c3c;
        }
        //按钮圆角
        .wd-button.is-round {
          border-radius: 13rpx;
        }
        //按钮文字样式
        .wd-button__text {
          font-size: 32rpx;
          font-weight: bold;
        }
      }
    }
  }
  //商品底部按钮结束位置
}
</style>
