<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
}
</route>

<template>
  <view class="overflow-hidden" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <view class="center pt-2 pb-29rpx font-bold text-32rpx font-[PingFangSC,PingFang_SC]">
      我的
    </view>
  </view>
  <image
    v-if="isLogin"
    :src="getImagesUrl('68594c51e4b00e5b2cf36187.png')"
    class="w750rpx h440rpx absolute left-0 top-0 z--1"
    mode="aspectFill"
  ></image>
  <view class="w700rpx h370rpx bg-white rounded-20rpx mx-25rpx center">
    <view v-if="isLogin" class="center flex-col gap-26rpx">
      <view v-if="userStore.userInfo?.avatar" class="userAvatar">
        <image
          class="w100% h100% rounded-50%"
          :src="isAvatar"
          round
          @click="goToObtainInformation"
        />
      </view>
      <image
        v-else
        class="w132rpx h131rpx rounded-50%"
        :src="isAvatar"
        round
        @click="goToObtainInformation"
      />
      <view class="text-36rpx font-500 leading-36rpx font-[SourceHanSerifCN] relative">
        <view>{{ isNickname }}</view>
        <image class="w32rpx h32rpx absolute right--50rpx top-0" :src="isGender" round />
      </view>
    </view>
    <view v-else class="w100% h100% center relative z1">
      <image
        class="w100% h100% absolute z--1"
        :src="getImagesUrl('68595444e4b00e5b2cf36188.png')"
      />
      <view class="absolute top-106rpx text-39rpx leading-36rpx color-#C02222 font-bold">
        欢迎来到沁为珠宝！
      </view>
      <view class="text-32rpx leading-32rpx font-500 font-[SourceHanSerifCN] color-#C02222">
        开启专属定制
      </view>
      <view class="absolute top-234rpx">
        <wd-button custom-class="w240rpx h79rpx rounded-15rpx!" :round="false" @click="checkLogin">
          授权登录
        </wd-button>
      </view>
    </view>
  </view>
  <view class="w700rpx overflow-auto bg-white rounded-20rpx mx-25rpx mt-20rpx pt-36rpx">
    <wd-cell-group custom-class="mx-10rpx">
      <block v-for="cell in cells" :key="cell.title">
        <wd-cell
          :title="cell.title"
          is-link
          center
          custom-class="py-36rpx"
          v-if="cell.title === '我的订单' ? isShowOrder : true"
          @click="handleCellClick(cell)"
        >
          <template #icon>
            <image class="w40rpx h40rpx mr-24rpx" :src="cell.icon"></image>
          </template>
        </wd-cell>
      </block>
    </wd-cell-group>
  </view>
  <image class="w90rpx h50rpx mt-29rpx block m-x-auto" src="../../static/about/qin.png" />
  <CustomerService ref="CustomerServiceElement" />
  <MessageBoxHb
    ref="MessageBoxHbElement"
    msg="确定要退出登陆吗？"
    cancelButtonText="我再想想"
    @confirm="confirm"
  />
</template>

<script lang="ts" setup>
import CustomerService from '@/components/CustomerService.vue'
import { useUserStore } from '@/store'
import { useLoginGuard } from '@/composables/useTheme'
import { maskPhone } from '@/utils/index'
import MessageBoxHb from '@/components/MessageBoxHb.vue'
import { orderShow, wxUserLogout } from '@/service/index/index'
import { getImagesUrl } from '@/utils/getImagesUrl'
// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()
const { checkLogin } = useLoginGuard()

const userStore = useUserStore()

const isLogin = computed(() => userStore.isLogined)
const isAvatar = computed(() =>
  userStore.userInfo?.avatar
    ? userStore.userInfo.avatar
    : getImagesUrl('685956dce4b00e5b2cf36189.png'),
)
const isGender = computed(() => {
  if (userStore.userInfo?.gender == '1') {
    return '../../static/about/male.png'
  } else if (userStore.userInfo?.gender == '2') {
    return '../../static/about/female.png'
  } else {
    return ''
  }
})
const isNickname = computed(() => {
  if (userStore.userInfo?.nickName) {
    return userStore.userInfo?.nickName
  } else if (userStore.userInfo?.phone) {
    return maskPhone(userStore.userInfo?.phone)
  } else {
    return '微信用户'
  }
})

const CustomerServiceElement = ref(null)
const cells = computed(() => (isLogin.value ? cellsTrue : cellsFalse))

const cellsTrue = [
  {
    icon: '../../static/about/archive.svg',
    title: '我的档案',
  },
  {
    icon: '../../static/about/order.svg',
    title: '我的订单',
  },
  {
    icon: '../../static/about/myDesign.svg',
    title: '我的设计',
  },
  {
    icon: '../../static/about/contactService.svg',
    title: '联系客服',
  },
  {
    icon: '../../static/about/feedback.svg',
    title: '意见反馈',
  },
  {
    icon: '../../static/about/logout.svg',
    title: '退出登录',
  },
]

const cellsFalse = [
  {
    icon: '../../static/about/archive.svg',
    title: '我的档案',
  },
  {
    icon: '../../static/about/order.svg',
    title: '我的订单',
  },
  {
    icon: '../../static/about/myDesign.svg',
    title: '我的设计',
  },
  {
    icon: '../../static/about/contactService.svg',
    title: '联系客服',
  },
  {
    icon: '../../static/about/feedback.svg',
    title: '意见反馈',
  },
]

// 处理单元格点击事件
function handleCellClick(cell) {
  switch (cell.title) {
    case '我的档案':
      if (!checkLogin()) break
      uni.navigateTo({ url: '/pages/myArchive/index' })
      break
    case '我的订单':
      if (!checkLogin()) break
      uni.navigateTo({ url: '/pages/myOrder/index' })
      break
    case '我的设计':
      if (!checkLogin()) break
      uni.navigateTo({ url: '/pages/myDesign/index' })
      break
    case '联系客服':
      showMessageBox()
      break
    case '意见反馈':
      if (!checkLogin()) break
      uni.navigateTo({ url: '/pages/feedback/index' })
      break
    case '退出登录':
      showMessageBoxHbElement()
      break
  }
}

const isShowOrder = ref(false)
onShow(() => {
  const { data, run } = useRequest<boolean>(() => orderShow())
  run().then(() => {
    isShowOrder.value = data.value
  })
})

// 引入消息框组件
const MessageBoxHbElement = ref<InstanceType<typeof MessageBoxHb> | null>(null)
// 显示消息框
function showMessageBoxHbElement() {
  if (MessageBoxHbElement.value) {
    MessageBoxHbElement.value.open()
  }
}
// 确认删除
function confirm() {
  const { data, run } = useRequest<boolean>(() => wxUserLogout())
  run().then(() => {
    if (data.value) {
      userStore.clearUserInfo()
      uni.showToast({ icon: 'none', title: '退出登陆成功！' })
    } else {
      uni.showToast({ icon: 'none', title: '退出登陆失败，请联系客服！' })
    }
  })
}

// 显示消息框
function showMessageBox() {
  if (CustomerServiceElement.value) {
    CustomerServiceElement.value.open()
  }
}
// 获取头像和昵称
function goToObtainInformation() {
  uni.navigateTo({ url: '/pages/obtainInformation/index' })
}
</script>

<style lang="scss" scoped>
:deep(.wd-cell__wrapper) {
  padding: 0 !important;
}
:deep(.wd-cell__title) {
  display: flex;
  align-items: center;
  font-family: SourceHanSerifCN;
  font-size: 30rpx !important;
  font-weight: bold;
  line-height: 30rpx;
  color: #1e2127;
}

.userAvatar {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  padding: 10rpx;
  // margin-right: 30rpx;
  overflow: hidden;
  background-color: #fff;
  background-image: url('/static/about/avtar.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-clip: border-box;
  background-size: 100% 100%;
  border: 8rpx solid transparent;
  border-radius: 50%;
}
</style>
