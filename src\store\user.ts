import { defineStore } from 'pinia'
import { ref } from 'vue'

const initState = { nickName: '', avatar: '', gender: '', token: '', phone: '', customized: false }

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...initState })

    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = { ...userInfo.value, ...val }
    }

    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }
    // 一般没有reset需求，不需要的可以删除
    const reset = () => {
      userInfo.value = { ...initState }
    }
    const isLogined = computed(() => !!userInfo.value.token)

    const camelCase = ref({})
    const useData = () => {
      return {
        setData: (key, data) => (camelCase.value[key] = data),
        getData: (key) => camelCase.value[key],
        clearData: (key) => delete camelCase.value[key],
      }
    }

    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      isLogined,
      reset,
      useData,
    }
  },
  {
    persist: true,
  },
)
