<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '自由定制',
  },
}
</route>

<template>
  <view class="container">
    <view class="canvas-container">
      <!-- 网格背景辅助线 -->
      <view class="grid-background"></view>
      <canvas
        canvas-id="myCanvas"
        id="myCanvas"
        type="2d"
        :class="state.canvasClass"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      ></canvas>
      <!-- 悬浮的新手引导按钮 -->
      <view class="floating-guide-button" @click="showDemoGuide">
        <view class="guide-icon">
          <image class="guide-icon-image" src="@/static/diy/xsyd.svg" mode="aspectFit" />
        </view>
        <text class="guide-text">新手引导</text>
      </view>
    </view>
    <view class="bottom-container">
      <!-- 操作按钮区域 -->
      <view class="action-area">
        <view class="action-buttons-container">
          <view class="action-buttons">
            <wd-button
              custom-class="!bg-#fff shadow-md !text-40rpx !border-rd-15rpx !opacity-100"
              type="icon"
              :round="false"
              icon="arrow-left"
              @click="selectNextBead"
              :disabled="state.selectedIndex === -1"
            ></wd-button>
            <wd-button
              custom-class="!bg-#fff shadow-md !text-40rpx !border-rd-15rpx !opacity-100"
              type="icon"
              :round="false"
              icon="arrow-right"
              @click="selectPrevBead"
              :disabled="state.selectedIndex === -1"
            ></wd-button>
            <wd-button
              custom-class="!bg-#fff shadow-md !text-40rpx !border-rd-15rpx !opacity-100"
              type="icon"
              :round="false"
              icon="close-normal"
              @click="clearSelection"
              :disabled="state.selectedIndex === -1"
            ></wd-button>
          </view>
          <view class="flex gap-20rpx">
            <wd-button
              custom-class="!bg-#fff shadow-md !border-rd-15rpx font-bold !opacity-100"
              type="info"
              :round="false"
              @click="cropBeads(false)"
            >
              裁剪
            </wd-button>
            <wd-button
              type="primary"
              custom-class="shadow-md !border-rd-15rpx"
              :round="false"
              @click="onComplete"
            >
              完成
            </wd-button>
          </view>
        </view>
      </view>

      <!-- Tabs区域 -->
      <view class="tabs-area">
        <myWdTabs
          v-model="tabValue"
          size="large"
          animated
          slidable="always"
          color="#e63b3c"
          lineWidth="50rpx"
          @change="bigTypeChange"
        >
          <template #right>
            <wd-picker
              :columns="columnsColor"
              label="单列选项"
              v-model="colorId"
              value-key="materiaId"
              title="请选择色系"
              label-key="materiaName"
              confirm-button-text="确定"
              use-default-slot
              @confirm="handleConfirm"
            >
              <wd-button custom-class="text-28rpx" type="text" v-if="activeBigType == 0">
                按色系筛选
              </wd-button>
            </wd-picker>
          </template>
          <myWdTab
            :title="'选' + item.attributeName"
            v-for="item in dataList"
            :key="item.attributeName"
            @change="tabsChange"
          >
            <myWdTabs
              ref="tabRef"
              v-model="item.tab"
              size="large"
              indicator="bg"
              color="#e63b3c"
              :tabsBg="getImagesUrl('685f7b76e4b00e5b211a8ce9.png')"
              :tabActiveBg="getImagesUrl('685f7b82e4b00e5b211a8cea.png')"
              tabWidth="121rpx"
              tabsHeight="86rpx"
              tabHeight="88rpx"
            >
              <block v-for="(tab, idx) in item.materialVO" :key="tab.materiaName">
                <myWdTab
                  :title="tab.materiaName"
                  :custom-class="`my-tab-bg my-tab-bg-${idx}`"
                  @change="typeChange"
                >
                  <scroll-view scroll-x>
                    <view class="w100% flex gap-50rpx rpx-20rpx pt-10rpx">
                      <view
                        v-for="im in beadBaseinfoVO"
                        class="min-w120rpx flex flex-col gap-20rpx justify-center items-center"
                        :key="im.uniqueIndex"
                        @click="showBeadInfo(im)"
                      >
                        <wd-img
                          :width="60"
                          :height="60"
                          :src="im.images"
                          custom-class="!center"
                          mode="heightFix"
                        />
                        <view class="text-28rpx whitespace-nowrap">{{ im.name }}</view>
                      </view>
                    </view>
                  </scroll-view>
                </myWdTab>
              </block>
            </myWdTabs>
          </myWdTab>
        </myWdTabs>
      </view>
    </view>
    <wd-gap safe-area-bottom height="0"></wd-gap>
  </view>

  <!-- 引导演示组件 -->
  <GuideDemo v-if="showGuideDemoFlag" @close="closeGuideDemoHandler" />

  <!-- 自定义底部弹出层 -->
  <wd-popup
    v-model="showPopup"
    custom-style="border-radius:32rpx;padding-left: 20rpx;padding-right: 20rpx"
    safe-area-inset-bottom
    position="bottom"
    @close="closePopup"
  >
    <view v-if="beadInfo" class="mb-60rpx">
      <!-- 珠子基本信息 -->
      <view class="bead-info-section">
        <view class="bead-avatar-container">
          <image
            class="bead-avatar-image"
            :src="beadInfo?.picture"
            custom-class="!center"
            mode="heightFix"
          />
        </view>
        <view class="bead-details">
          <view class="bead-name">{{ beadInfo?.name }}</view>
          <view class="bead-material">材质：{{ beadInfo?.materialName }}</view>
        </view>
        <wd-button
          type="info"
          custom-class="!min-w-50rpx !w150rpx rounded-15rpx!"
          @click="onGemDetail(beadInfo.id)"
        >
          <span class="mr-10rpx">详情</span>
          <wd-icon name="arrow-right" size="28rpx" />
        </wd-button>
      </view>

      <!-- 尺寸和价格选择 -->
      <view class="size-price-list">
        <wd-row class="size-price-item" v-for="sku in beadInfo.skuList" :key="sku.id">
          <wd-col :span="10">
            <view class="size-name">{{ sku.diameter + 'mm' }}</view>
          </wd-col>
          <wd-col :span="12">
            <view class="price">单价:￥{{ sku.price }}</view>
          </wd-col>
          <wd-col :span="2">
            <wd-icon
              name="add-circle"
              size="40rpx"
              color="#e73c3c"
              @click="addToBracket(beadInfo, sku)"
            />
          </wd-col>
        </wd-row>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="w100% flex gap-20rpx">
      <wd-button
        custom-class="!min-w-50rpx w150rpx  rounded-15rpx!"
        type="info"
        icon="rollback"
        @click="cropBeads(true)"
      ></wd-button>
      <wd-button custom-class="flex-1 rounded-15rpx!" type="primary" @click="closePopup">
        完成
      </wd-button>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { getZydzList } from '@/service/mock'
// import { gemTypeList } from '@/service/custom'
import { useMessage } from 'wot-design-uni'
import myWdTabs from '@/components/wd-tabs/my-wd-tabs.vue'
import myWdTab from '@/components/wd-tabs/wd-tab/my-wd-tab.vue'
import GuideDemo from '@/components/GuideDemo.vue'
// import bgImg from '@/static/diy/tab_bg.png'
// import activeBg from '@/static/diy/tab_bg_sel.png'
import {
  IGemTypeListItem,
  gemTypeList,
  IGemEnjoyColorItem,
  gemList,
  gemDetail,
  IGemDetailItem,
} from '@/service/gemLibrary'
import { useLoginGuard } from '@/composables/useTheme'
import { getImagesUrl } from '@/utils/getImagesUrl'

const { checkLogin } = useLoginGuard()

let canvasEl = null
let ctx = null
const colorId = ref('')
const columnsColor = ref([])

// 珠子图片间距
const clipRatio = 0.98
// 空孔间距
const holeClipRatio = 0.94

const state = reactive({
  beadSize: 20, // 珠子大小
  totalBeads: 18, // 初始珠子孔位数量
  maxBeads: 40, // 最大珠子数量
  selectedBeads: 4, // 初始选中的珠子数量
  selectedIndex: -1, // 当前选中的珠子索引 (-1 表示未选中)
  beads: [], // 珠子数组，null 表示空位
  gapAngle: (Math.PI / 180) * 0, // 珠子间的间隔角度
  touchPosition: null, // 存储最近一次触摸的位置
  scale: 1, // 缩放比例
  rotation: 0, // 旋转角度（弧度）
  lastTouches: [],
  transformOrigin: { x: 150, y: 150 },
  canvasReady: false,
  animationTimer: null,
  translateY: 0,
  isInAnim: false,
  moveAnima: null,
  canvasClass: 'bracelet-canvas',
  nextAddPosition: -1, // 下一个添加珠子的位置索引
  beadInfoArray: [], // 存储珠子信息
  actualRadius: null, // 保存实际使用的半径
  isProcessingDrawTasks: false, // 标记是否正在处理绘制任务
  beadDrawTaskTimer: null,
})

const beadInfo = ref(null)
const isColor = ref(false)

// 汇总信息对象
const summaryInfo = reactive({
  totalCount: 0,
  circumference: 0,
  recommendedGender: '',
  gender: 0, // 0:女 1:男
  totalCost: 0,
  handworkFee: 50,
  beadDetails: [],
})

// 初始化珠子
const initBeads = () => {
  // 检查是否已经有珠子数据（从详情页恢复的）
  const hasExistingBeads =
    state.beads && state.beads.length > 0 && state.beads.some((bead) => bead !== null)

  if (!hasExistingBeads) {
    // 只有在没有现有珠子数据的情况下才初始化珠子
    state.beads = Array.from({ length: state.totalBeads }, () => null)
    state.nextAddPosition = 0
  } else {
    // 如果有现有数据，设置下一个添加位置
    const existingBeads = state.beads.filter((bead) => bead && !bead.isEmptyHole)
    state.nextAddPosition = existingBeads.length < state.totalBeads ? existingBeads.length : -1
  }

  state.beadInfoArray = []
  state.actualRadius = null

  // 更新汇总信息
  updateSummaryInfo()
}

const originalState = {
  scale: 1,
  translateY: 0,
}

let isPinching = false
let pinchInitialDistance = 0
let pinchInitialScale = 1
let pinchInitialAngle = 0
let pinchInitialRotation = 0

// 触摸事件处理函数
const handleTouchStart = (event) => {
  if (event.touches && event.touches.length > 0) {
    // 保存当前触摸点
    state.lastTouches = []
    for (let i = 0; i < event.touches.length; i++) {
      state.lastTouches.push({
        pageX: event.touches[i].pageX,
        pageY: event.touches[i].pageY,
      })
    }

    // 记录双指缩放初始状态，避免首帧抖动
    if (event.touches.length === 2) {
      isPinching = true
      pinchInitialDistance = 0
    }

    // 单点触摸 - 用于选择珠子
    if (event.touches.length === 1) {
      const touch = event.touches[0]

      try {
        const systemInfo = uni.getSystemInfoSync()
        const query = uni.createSelectorQuery().select('#myCanvas')
        query
          .boundingClientRect((data: any) => {
            if (data) {
              console.log('Canvas位置:', data)

              // 检查点击的Y坐标位置，如果超出canvas高度并进入底部区域，则不处理点击
              if (touch.pageY > data.top + data.height) {
                console.log('点击在底部区域，不处理珠子选择')
                return
              }

              const x = touch.pageX - data.left
              const y = touch.pageY - data.top

              console.log('触摸点坐标:', x, y, '原始坐标:', touch.pageX, touch.pageY)

              state.touchPosition = { x, y }

              checkBeadTouch(x, y)
            } else {
              console.error('无法获取Canvas位置信息')
              const x = touch.pageX
              const y = touch.pageY
              console.log('使用原始触摸坐标:', x, y)
              state.touchPosition = { x, y }
              checkBeadTouch(x, y)
            }
            // drawBracelet()
            smoothAnimate(state.scale, 0)
          })
          .exec()
      } catch (err) {
        console.error('获取坐标时出错:', err)
      }
    }
  }
}

// 触摸移动处理
const handleTouchMove = (event) => {
  if (event.touches && event.touches.length === 2 && isPinching) {
    const [touch1, touch2] = [event.touches[0], event.touches[1]]
    const currentDistance = Math.hypot(touch1.pageX - touch2.pageX, touch1.pageY - touch2.pageY)
    const currentAngle = Math.atan2(touch2.pageY - touch1.pageY, touch2.pageX - touch1.pageX)

    if (pinchInitialDistance === 0) {
      pinchInitialDistance = currentDistance
      pinchInitialScale = state.scale
      pinchInitialAngle = currentAngle
      pinchInitialRotation = state.rotation
      return
    }
    const distanceRatio = currentDistance / (pinchInitialDistance || 1)
    const targetScale = Math.max(0.5, Math.min(2.5, pinchInitialScale * distanceRatio))
    const rotationDelta = currentAngle - pinchInitialAngle
    const targetRotation = (pinchInitialRotation + rotationDelta) % (Math.PI * 2)

    // 动画平滑度，数值越大越跟手
    const smoothFactor = 0.1
    state.scale = state.scale + (targetScale - state.scale) * smoothFactor
    state.rotation = state.rotation + (targetRotation - state.rotation) * smoothFactor

    // 在下一帧绘制
    scheduleDraw()
    return
  }
  // 检测是否是多点触摸（2个手指）
  if (event.touches && event.touches.length === 2 && state.lastTouches.length === 2) {
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]
    const lastTouch1 = state.lastTouches[0]
    const lastTouch2 = state.lastTouches[1]
    const currentDistance = Math.sqrt(
      Math.pow(touch1.pageX - touch2.pageX, 2) + Math.pow(touch1.pageY - touch2.pageY, 2),
    )
    const lastDistance = Math.sqrt(
      Math.pow(lastTouch1.pageX - lastTouch2.pageX, 2) +
        Math.pow(lastTouch1.pageY - lastTouch2.pageY, 2),
    )
    const scaleChange = currentDistance / lastDistance
    const newScale = Math.max(0.5, Math.min(2.5, state.scale * scaleChange))

    const lastAngle = Math.atan2(
      lastTouch2.pageY - lastTouch1.pageY,
      lastTouch2.pageX - lastTouch1.pageX,
    )
    const currentAngle = Math.atan2(touch2.pageY - touch1.pageY, touch2.pageX - touch1.pageX)
    const rotationChange = currentAngle - lastAngle

    state.scale = newScale
    state.rotation = (state.rotation + rotationChange) % (Math.PI * 2)

    state.lastTouches = []
    for (let i = 0; i < event.touches.length; i++) {
      state.lastTouches.push({
        pageX: event.touches[i].pageX,
        pageY: event.touches[i].pageY,
      })
    }

    // 使用节流的方式在下一帧绘制，避免高频刷新导致图片闪烁
    scheduleDraw()
  }
}

// 判断图片是否为圆形（正方形）
const isCircularBead = (image) => {
  if (!image || !image.width || !image.height) return true

  const aspectRatio = image.width / image.height
  // 宽高比在 0.8 到 1.25 之间认为是圆形珠子
  console.log(
    '是否是圆形珠子',
    aspectRatio >= 0.8 && aspectRatio <= 1.25,
    image.width,
    image.height,
  )
  return aspectRatio >= 0.8 && aspectRatio <= 1.25
}

// 计算珠子图片的实际显示尺寸（返回宽度和高度）
const calculateActualImageSize = (bead, beadSize) => {
  if (!bead || bead.isEmptyHole || !bead.imagePath) {
    return { width: beadSize * 2, height: beadSize * 2 } // 空孔或无图片时使用圆形区域
  }

  // 尝试多种可能的缓存键
  const possibleUrls = [
    `${bead.imagePath}&uid=${bead.uniqueId || Date.now()}`,
    `${bead.imagePath}&uid=${bead.uniqueId}`,
    bead.imagePath,
  ]

  let cachedImage = null
  for (const url of possibleUrls) {
    if (beadImageCache[url]) {
      cachedImage = beadImageCache[url]
      break
    }
  }

  if (cachedImage && cachedImage.width && cachedImage.height) {
    const imgW = cachedImage.width
    const imgH = cachedImage.height

    if (isCircularBead(cachedImage)) {
      // 圆形珠子：保持现在的效果，最长边等于 beadSize*2
      const maxSide = beadSize * 2
      const scale = Math.min(maxSide / imgW, maxSide / imgH)
      const drawW = imgW * scale
      const drawH = imgH * scale
      return { width: drawW, height: drawH }
    } else {
      // 长方形珠子：宽度按图片比例，高度保持和圆形一致
      const circularHeight = beadSize * 2 // 和圆形珠子高度一致
      const scale = circularHeight / imgH // 根据高度计算缩放比例

      const drawW = imgW * scale // 宽度按比例缩放
      const drawH = circularHeight // 高度固定

      return { width: drawW, height: drawH }
    }
  }

  // 如果没有缓存图片，假设是正方形图片
  return { width: beadSize * 2, height: beadSize * 2 }
}

// 检查点击是否在矩形图片范围内
const isPointInImageRect = (pointX, pointY, centerX, centerY, imageSize) => {
  const halfWidth = imageSize.width / 2
  const halfHeight = imageSize.height / 2

  return (
    pointX >= centerX - halfWidth &&
    pointX <= centerX + halfWidth &&
    pointY >= centerY - halfHeight &&
    pointY <= centerY + halfHeight
  )
}

// 检查触摸点是否在珠子上
const checkBeadTouch = (touchX, touchY) => {
  if (!ctx || !canvasEl) {
    console.error('Canvas未初始化，无法检查触摸')
    return
  }
  if (!state.beadInfoArray) {
    recalculateBeadPositions()
  }
  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y
  const relX = touchX - centerX
  const relY = touchY - centerY
  const cos = Math.cos(-state.rotation)
  const sin = Math.sin(-state.rotation)
  const rotatedX = relX * cos - relY * sin
  const rotatedY = relX * sin + relY * cos

  const scaledX = rotatedX / state.scale
  const scaledY = rotatedY / state.scale
  const transformedX = scaledX + centerX
  const transformedY = scaledY + centerY
  console.log('触摸变换:', '原始:', touchX, touchY, '变换后:', transformedX, transformedY)

  const tolerance = 1.0

  let closestBeadIndex = -1
  let minDistance = Infinity

  // 使用预先计算好的珠子位置信息检查点击
  for (const info of state.beadInfoArray) {
    const x = info.centerX
    const y = info.centerY
    const distance = Math.sqrt(Math.pow(transformedX - x, 2) + Math.pow(transformedY - y, 2))

    // 记录最近的珠子
    if (distance < minDistance) {
      minDistance = distance
      closestBeadIndex = info.index
    }

    // 检查是否点击在珠子图片范围内
    let isInTouchArea = false
    if (info.bead && !info.bead.isEmptyHole && info.bead.imagePath) {
      // 对于有珠子图片的情况，只检测图片范围，不检测珠子孔
      const imageSize = calculateActualImageSize(info.bead, info.beadSize)
      isInTouchArea = isPointInImageRect(transformedX, transformedY, x, y, imageSize)
    } else {
      // 对于空孔或没有图片的珠子，使用圆形范围检测
      const actualBeadSize = info.beadSize * holeClipRatio * tolerance
      isInTouchArea = distance <= actualBeadSize
    }

    if (isInTouchArea) {
      console.log(
        '选中珠子:',
        info.index,
        '位置:',
        x,
        y,
        '距离:',
        distance,
        '触摸方式:',
        info.bead && !info.bead.isEmptyHole ? '图片范围' : '圆形范围',
      )
      toggleBead(info.index)
      return // 找到匹配的珠子后立即返回
    }
  }

  if (closestBeadIndex !== -1) {
    const closestInfo = state.beadInfoArray.find((info) => info.index === closestBeadIndex)

    // 使用扩展的触摸范围作为备选方案
    let maxAcceptableDistance
    if (closestInfo.bead && !closestInfo.bead.isEmptyHole && closestInfo.bead.imagePath) {
      // 对于有珠子图片的情况，使用图片尺寸的1.2倍作为备选范围
      const imageSize = calculateActualImageSize(closestInfo.bead, closestInfo.beadSize)
      maxAcceptableDistance = (Math.max(imageSize.width, imageSize.height) / 2) * 1.2
    } else {
      // 对于空孔或没有图片的珠子，使用原来的逻辑
      maxAcceptableDistance = closestInfo.beadSize * holeClipRatio * 1.2
    }

    console.log(
      '最近珠子:',
      closestBeadIndex,
      '距离:',
      minDistance,
      '最大可接受距离:',
      maxAcceptableDistance,
    )

    if (minDistance <= maxAcceptableDistance) {
      console.log('选择最近的珠子:', closestBeadIndex)
      toggleBead(closestBeadIndex)
    } else {
      console.log('点击位置超出所有珠子的可接受范围')
    }
  }
}

// 切换珠子状态
const toggleBead = (index) => {
  console.log('切换珠子状态，当前选中:', state.selectedIndex, '新选中:', index)

  if (state.selectedIndex === index) {
    // 如果两次点击同一个珠子，则取消选择
    state.selectedIndex = -1
    console.log('取消选择')
  } else {
    // 选择新的珠子
    state.selectedIndex = index
    console.log('新选中珠子:', index)
  }

  // 重置绘制任务状态，确保每次点击都会触发珠子绘制
  state.isProcessingDrawTasks = false
  drawBracelet()
}

const handleTouchEnd = (event) => {
  if (!event.touches || event.touches.length < 2) {
    isPinching = false
  }
}

// 选择前一个珠子孔位
const selectPrevBead = () => {
  if (state.selectedIndex === -1) return // 如果没有选中，不执行操作

  const totalPositions = state.beads.length

  // 计算前一个位置的索引，如果是第一个则循环到最后一个
  const prevIndex = (state.selectedIndex - 1 + totalPositions) % totalPositions
  state.selectedIndex = prevIndex
  smoothAnimate(state.scale, 0)
}

// 选择后一个珠子孔位
const selectNextBead = () => {
  if (state.selectedIndex === -1) return // 如果没有选中，不执行操作

  // 获取当前所有珠子的数量
  const totalPositions = state.beads.length

  // 计算后一个位置的索引，如果是最后一个则循环到第一个
  const nextIndex = (state.selectedIndex + 1) % totalPositions
  state.selectedIndex = nextIndex
  smoothAnimate(state.scale, 0)
}

// 清除当前选中的珠子
const clearSelection = () => {
  if (state.selectedIndex === -1) return // 如果没有选中，不执行操作

  // 检查选中位置是否有珠子
  if (state.beads[state.selectedIndex] && !state.beads[state.selectedIndex].isEmptyHole) {
    // 获取当前选中的珠子信息
    const bead = state.beads[state.selectedIndex]

    // 保留珠子的尺寸信息，创建一个空位标记对象
    state.beads[state.selectedIndex] = {
      isEmptyHole: true,
      originalSize: bead.size || 12,
      originalBeadId: bead.id,
      removedBead: true,
    }
    const totalBeadCount = state.beads.length
    if (totalBeadCount <= 3) {
      // 3颗及以下位置使用特殊布局
      applySpecialLayout(totalBeadCount)
    } else {
      // 超过3颗位置时保持圆形布局
      recalculateBeadPositions()
    }
    smoothAnimate(state.scale, 0)

    // 更新汇总信息
    updateSummaryInfo()
  }
}

// 特殊布局
const applySpecialLayout = (beadCount) => {
  if (!canvasEl || !ctx) {
    console.error('Canvas未初始化，无法应用特殊布局')
    return
  }

  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y
  const size = Math.min(canvasEl.width, canvasEl.height)
  const referenceRadius = size * 0.35
  const baseBeadSize = referenceRadius * 0.18

  const beadInfoArray = []

  let beadSize1, beadSize2, spacing2, avgSize3, radius3, angle, beadSize

  switch (beadCount) {
    case 1: // 单珠
      // 单珠居中放置
      beadInfoArray.push({
        index: 0,
        beadSize: state.beads[0].size ? baseBeadSize * (state.beads[0].size / 12) : baseBeadSize,
        hasBead: true,
        bead: state.beads[0],
        centerX,
        centerY,
        centerAngle: 0,
        beadAngle: 0,
      })
      break

    case 2: // 双珠垂直排列 - 紧贴在一起
      beadSize1 = state.beads[0].size ? baseBeadSize * (state.beads[0].size / 12) : baseBeadSize
      beadSize2 = state.beads[1].size ? baseBeadSize * (state.beads[1].size / 12) : baseBeadSize
      spacing2 = (beadSize1 + beadSize2) * 0.5

      beadInfoArray.push({
        index: 0,
        beadSize: beadSize1,
        hasBead: true,
        bead: state.beads[0],
        centerX,
        centerY: centerY - spacing2 / 2,
        centerAngle: -Math.PI / 2,
        beadAngle: 0,
      })

      beadInfoArray.push({
        index: 1,
        beadSize: beadSize2,
        hasBead: true,
        bead: state.beads[1],
        centerX,
        centerY: centerY + spacing2 / 2,
        centerAngle: Math.PI / 2,
        beadAngle: 0,
      })
      break

    case 3: // 三珠三角形布局 - 紧贴在一起
      avgSize3 =
        (((state.beads[0].size ? state.beads[0].size : 12) +
          (state.beads[1].size ? state.beads[1].size : 12) +
          (state.beads[2].size ? state.beads[2].size : 12)) /
          36) *
        baseBeadSize
      radius3 = avgSize3 * clipRatio
      for (let i = 0; i < 3; i++) {
        angle = (i * 2 * Math.PI) / 3 - Math.PI / 2
        beadSize = state.beads[i].size ? baseBeadSize * (state.beads[i].size / 12) : baseBeadSize

        beadInfoArray.push({
          index: i,
          beadSize,
          hasBead: true,
          bead: state.beads[i],
          centerX: centerX + radius3 * Math.cos(angle),
          centerY: centerY + radius3 * Math.sin(angle),
          centerAngle: angle,
          beadAngle: 0,
        })
      }
      break

    default:
      // 大于3颗珠子正常布局
      recalculateBeadPositions()
      return
  }
  state.beadInfoArray = beadInfoArray
}

const cropBeads = (allowUndo = false) => {
  console.log('cropBeads 执行')

  if (allowUndo && !state.beads.some((b) => b === null || (b && b.isEmptyHole))) {
    if (state.beads.length === 0) return

    let idx = state.beads.length - 1
    while (idx >= 0) {
      const bead = state.beads[idx]
      if (bead && !bead.isEmptyHole && !bead.isRemoving) {
        bead.isRemoving = true
        bead.appearProgress = bead.appearProgress !== undefined ? bead.appearProgress : 1
        bead.dropOffset = 0
        scheduleDraw()
        break
      }
      idx--
    }
    return
  }

  const existingBeads = state.beads.filter((bead) => bead && !bead.isEmptyHole)

  if (existingBeads.length === 0) {
    console.log('没有珠子可裁剪，重置为初始状态')
    initBeads()
    state.selectedIndex = -1
    state.beadInfoArray = []
    state.actualRadius = null
    smoothAnimate(state.scale, state.translateY)
    return
  }

  state.beads = existingBeads
  state.selectedIndex = -1
  state.beadInfoArray = []

  if (existingBeads.length <= 3) {
    applySpecialLayout(existingBeads.length)
  } else {
    recalculateBeadPositions()
  }

  // 更新下一个可添加位置
  if (existingBeads.length < state.maxBeads) {
    state.nextAddPosition = existingBeads.length
  } else {
    state.nextAddPosition = state.beads.findIndex((b) => b === null || b.isEmptyHole)
  }

  console.log('裁剪后的下一个添加位置:', state.nextAddPosition)
  // 更新汇总信息
  updateSummaryInfo()
  smoothAnimate(state.scale, state.translateY)
}

// 初始化canvas
const initCanvas = () => {
  try {
    console.log('开始初始化Canvas')

    // 获取屏幕尺寸和安全区域信息
    const systemInfo = uni.getSystemInfoSync()
    const screenWidth = systemInfo.windowWidth

    // 获取安全区域尺寸
    const safeAreaInsets = {
      top: systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.top : 0,
      bottom: systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0,
    }

    console.log('安全区域:', safeAreaInsets)
    // 计算底部区域高度
    const bottomAreaHeight = 220 + safeAreaInsets.bottom
    const availableHeight = systemInfo.windowHeight - bottomAreaHeight - safeAreaInsets.top
    console.log(
      '屏幕尺寸:',
      screenWidth,
      'x',
      systemInfo.windowHeight,
      '可用高度:',
      availableHeight,
      '底部区域:',
      bottomAreaHeight,
    )
    canvasEl = {
      width: screenWidth,
      height: availableHeight,
    }
    state.transformOrigin = {
      x: screenWidth / 2,
      y: availableHeight / 2,
    }
    try {
      const query = uni.createSelectorQuery()
      query
        .select('#myCanvas')
        .fields({ node: true, size: true }, (res: any) => {
          if (res && res.node) {
            const canvas = res.node
            canvasEl.canvas = canvas
            ctx = canvas.getContext('2d')
            canvas.width = screenWidth * systemInfo.pixelRatio
            canvas.height = availableHeight * systemInfo.pixelRatio
            ctx.scale(systemInfo.pixelRatio, systemInfo.pixelRatio)
            if (!ctx) {
              console.error('无法获取Canvas 2D上下文')
              return
            }
            console.log('获取Canvas 2D上下文成功:', ctx)
            state.scale = 1
            state.rotation = 0
            initBeads()
            state.canvasReady = true
            drawBracelet()
            canvasEl.node = canvas
          } else {
            console.error('无法获取Canvas节点')
            message.show('Canvas初始化失败，请重新进入页面')
          }
        })
        .exec()
    } catch (ctxError) {
      console.error('创建Canvas上下文出错:', ctxError)
      message.show('Canvas初始化出错，请重新进入页面')
    }
  } catch (error) {
    console.error('Canvas初始化出错:', error)
    canvasEl = null
    ctx = null
    state.canvasReady = false
  }
}

const dataList = ref([])
const tabValue = ref(0)
const tabsAction = ref(0) // 当前选中的类
const typeAction = ref(0) // 当前选中的标签
const actionTabData = ref(null) // 当前选中的类数据
const actionTabDataType = ref(null) // 当前选中的标签数据
const beadBaseinfoVO = ref(null) // 当前需要显示的宝石列表数据
const activeBigType = ref(0)

const tabRef = ref()
const bigTypeChange = ({ index }) => {
  activeBigType.value = index
  nextTick(() => {
    setTimeout(() => {
      if (tabRef.value && tabRef.value[index]) {
        tabRef.value[index].setActive(0)
      }
    }, 300) // 加点延迟，等彻底渲染完毕
  })
}

// 类切换事件
async function tabsChange(index) {
  tabsAction.value = index
  typeAction.value = 0
  actionTabData.value = dataList.value[index]
  actionTabDataType.value = actionTabData.value.materialVO[typeAction.value]
  beadBaseinfoVO.value = await getGemList(
    actionTabData.value.attributeId,
    actionTabDataType.value ? actionTabDataType.value.materiaId : null,
  )
}

// 标签切换事件
async function typeChange(index) {
  typeAction.value = index
  actionTabDataType.value = actionTabData.value.materialVO[typeAction.value]
  beadBaseinfoVO.value = await getGemList(
    actionTabData.value.attributeId,
    actionTabDataType.value ? actionTabDataType.value.materiaId : null,
  )
}
// 按色系触发
async function handleConfirm({ value }) {
  columnsColor.value = value
  beadBaseinfoVO.value = await getGemList(
    actionTabData.value.attributeId,
    actionTabDataType.value ? actionTabDataType.value.materiaId : null,
    value,
  )
}

const getDataList = async () => {
  try {
    const data = await getZydzList()
    console.log('res', data)
    let list: any = [
      { attributeId: '0', attributeName: '五行' },
      { attributeId: '2', attributeName: '材质' },
      { attributeId: '3', attributeName: '珠型' },
      { attributeId: '5', attributeName: '配饰' },
    ]
    // { attributeId: '1', attributeName: '色系' },

    // 两层嵌套获取数据 第一层获取tabs 第二层获取类型和标签获取具体的宝石列表。
    // 第一层
    list = await Promise.all(
      list.map(async (e) => {
        const materialVO = await getGemTypeList(e.attributeId)
        return {
          ...e,
          materialVO,
        }
      }),
    )
    const colorData = await getGemTypeList('1')
    columnsColor.value = [{ materiaId: '', materiaName: '全部' }, ...colorData]
    console.log(list, '最终结果')
    dataList.value = list.map((it) => ({ ...it, tab: 0 }))
    tabsChange(0)
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 获取宝石标签
async function getGemTypeList(id: string): Promise<any> {
  const { data, run } = useRequest<IGemTypeListItem[]>(() => gemTypeList(id))
  // 等待请求结束
  await run()
  const materialVO = data.value.map((e) => {
    return { materiaId: e.id, materiaName: e.typeName, attributeId: id }
  })
  return materialVO
}
// 获取宝石列表
async function getGemList(id: string, type?: string, colorId?: string): Promise<any> {
  const { data, run } = useRequest<IGemEnjoyColorItem[]>(() => gemList({ type: id, colorId }))
  await run()
  const dataList = data.value.length ? data.value.filter((e) => e.id === type)[0].list : []
  const beadBaseinfoVO = dataList.map((e) => {
    return { uniqueIndex: e.id, images: e.picture, name: e.name }
  })
  return beadBaseinfoVO
}

onLoad(() => {
  getDataList()
  // 检查是否有详情数据需要恢复
  const { getData, clearData } = useData()
  const detailData = getData('dzxq') // 获取详情数据
  console.log('detailData', detailData)
  if (detailData) {
    // 如果详情数据传参则恢复手串样式
    state.beads = detailData.beads
    state.totalBeads = detailData.beads.length
    updateSummaryInfo() // 更新汇总信息

    // 清除数据，避免重复恢复
    clearData('dzxq')
  }

  console.log('组件挂载完成，准备初始化Canvas')
  setTimeout(() => {
    console.log('开始尝试初始化Canvas')
    initCanvas()
  }, 300)
})
onBeforeUnmount(() => {
  if (state.animationTimer) {
    clearTimeout(state.animationTimer)
  }
  canvasEl = null
  ctx = null
})

const showBeadInfo = async (item) => {
  const { data, run } = useRequest<IGemDetailItem>(() => gemDetail(item.uniqueIndex))
  await run()
  beadInfo.value = data.value
  const material = data.value.typeList.find((it: any) => it.type === 2)
  const materialName = material.typeName
  beadInfo.value.materialName = materialName
  console.log('beadInfo', beadInfo.value)
  showPopup.value = true
  if (canvasEl && ctx) {
    originalState.scale = state.scale
    try {
      state.canvasClass = 'bracelet-canvas'
      const targetScale = state.scale * 0.7
      smoothAnimate(targetScale, -60)
    } catch (err) {
      console.error('计算出错:', err)
    }
  } else {
    console.log('Canvas 或 ctx 尚未初始化，跳过动画')
  }
}

const addToBracket = (beadInfo, sku) => {
  console.log('添加到手串:', beadInfo, sku, state.nextAddPosition)
  const timestamp = new Date().getTime()
  const imagePath = beadInfo.picture + '?t=' + timestamp
  const bead = {
    id: beadInfo.uniqueIndex,
    name: beadInfo.name,
    image: beadInfo.picture,
    imagePath,
    code: sku.code, // SKU编码
    size: sku.diameter, // 直径
    materialName: beadInfo.materialName,
    price: sku.price,
    skuId: sku.id, // 保存 sku.id
    uniqueId: timestamp, // 添加唯一标识
    dropOffset: 20, // 下落动画初始偏移
    appearProgress: 0, // 图片渐显进度 0~1
    imageLoaded: false,
  }

  const actualBeadCount = state.beads.filter((b) => b !== null && !b.isEmptyHole).length
  const totalPositionsCount = state.beads.length
  console.log(
    '当前珠子数量:',
    actualBeadCount,
    '总位置数:',
    totalPositionsCount,
    '最大珠子数量:',
    state.maxBeads,
  )

  const isSelectedPositionValid =
    state.selectedIndex !== -1 && state.selectedIndex < state.beads.length
  const hasBeadAtSelectedPosition =
    isSelectedPositionValid &&
    state.beads[state.selectedIndex] &&
    !state.beads[state.selectedIndex]?.isEmptyHole

  const canAddAtSelectedPosition =
    isSelectedPositionValid &&
    (state.beads[state.selectedIndex] === null ||
      (state.beads[state.selectedIndex] && state.beads[state.selectedIndex]?.isEmptyHole))

  if (
    totalPositionsCount >= state.maxBeads &&
    !canAddAtSelectedPosition &&
    !hasBeadAtSelectedPosition
  ) {
    message.show('已达到最大珠子数量限制(40颗)，只能在现有空位添加或替换已有珠子')
    return
  }

  if (
    actualBeadCount >= state.maxBeads &&
    !canAddAtSelectedPosition &&
    !hasBeadAtSelectedPosition
  ) {
    message.show('已达到最大珠子数量限制(40颗)')
    return
  }

  let positionIndex = state.selectedIndex

  console.log('选中位置索引:', positionIndex, '是否有效:', positionIndex !== -1)
  console.log('选中位置是否可添加:', canAddAtSelectedPosition)
  console.log('选中位置是否已有珠子:', hasBeadAtSelectedPosition)

  const isUserSelectedPosition = canAddAtSelectedPosition || hasBeadAtSelectedPosition

  // 如果选中位置有珠子，则直接替换
  if (hasBeadAtSelectedPosition) {
    // 直接替换该位置的珠子
    state.beads[positionIndex] = bead
  } else if (!canAddAtSelectedPosition) {
    if (state.nextAddPosition !== undefined && state.nextAddPosition !== -1) {
      positionIndex = state.nextAddPosition
      console.log('是这里吗？1', positionIndex)
      if (
        positionIndex < state.beads.length &&
        state.beads[positionIndex] !== null &&
        !state.beads[positionIndex].isEmptyHole
      ) {
        positionIndex = -1
      }
    }

    if (positionIndex === -1) {
      const filledHolesCount = Math.min(
        state.beads.filter((b) => b !== null && !b.isEmptyHole).length,
        state.totalBeads,
      )
      const isAllInitialHolesFilled = filledHolesCount >= state.totalBeads
      if (isAllInitialHolesFilled) {
        if (totalPositionsCount >= state.maxBeads) {
          positionIndex = state.beads.findIndex((b) => b === null || b.isEmptyHole)
          if (positionIndex === -1) {
            message.show('没有可用的空位置，请先移除一些珠子或选择替换已有珠子')
            return
          }
        } else {
          positionIndex = state.beads.length
          state.beads.push(null)
        }
      } else {
        positionIndex = state.beads.findIndex((b) => b === null || b.isEmptyHole)
        if (positionIndex === -1) {
          if (totalPositionsCount >= state.maxBeads) {
            message.show('已达到最大珠子数量限制(40颗)')
            return
          }
          positionIndex = state.beads.length
          state.beads.push(null)
        }
      }
    }
  }

  console.log('添加的珠子位置下标', positionIndex)

  state.beads[positionIndex] = bead

  const isNowAllInitialHolesFilled =
    Math.min(state.beads.filter((b) => b !== null && !b.isEmptyHole).length, state.totalBeads) >=
    state.totalBeads

  if (isUserSelectedPosition) {
    state.selectedIndex = -1
  } else {
    let nextAddPosition = -1
    if (isNowAllInitialHolesFilled) {
      if (state.beads.length >= state.maxBeads) {
        nextAddPosition = state.beads.findIndex((b) => b === null || b.isEmptyHole)
      } else {
        nextAddPosition = positionIndex + 1
      }
    } else {
      if (positionIndex >= state.totalBeads - 1) {
        const firstEmptyPosition = state.beads
          .slice(0, state.totalBeads)
          .findIndex((b) => b === null || b.isEmptyHole)

        nextAddPosition = firstEmptyPosition !== -1 ? firstEmptyPosition : -1
      } else {
        const nextEmptyRelativePosition = state.beads
          .slice(positionIndex + 1, state.totalBeads)
          .findIndex((b) => b === null || b.isEmptyHole)
        if (nextEmptyRelativePosition !== -1) {
          nextAddPosition = positionIndex + 1 + nextEmptyRelativePosition
        }
      }
    }
    state.nextAddPosition = nextAddPosition
    state.selectedIndex = -1
  }
  const totalBeadCount = state.beads.length
  if (totalBeadCount <= 3) {
    applySpecialLayout(totalBeadCount)
  } else {
    recalculateBeadPositions()
  }

  state.isProcessingDrawTasks = false
  smoothAnimate(state.scale, state.translateY)
  // 延迟等回落动画结束后复位
  setTimeout(() => {
    smoothAnimate(state.scale, state.translateY)
  }, 260)

  // 更新汇总信息
  updateSummaryInfo()
}

// 更新汇总信息
const updateSummaryInfo = () => {
  // 计算已选中的珠子数量
  const selectedBeads = state.beads.filter((bead) => bead !== null && !bead.isEmptyHole)
  summaryInfo.totalCount = selectedBeads.length

  // 计算周长（单位：mm）
  let totalDiameter = 0
  selectedBeads.forEach((bead) => {
    if (bead && bead.size) {
      totalDiameter += parseFloat(bead.size) || 1
    } else {
      totalDiameter += 1
    }
  })
  summaryInfo.circumference = Math.round(totalDiameter * 100) / 100 // 保留两位小数，单位：mm

  // 推荐佩戴性别（固定周长140mm为分界线，约14cm）
  const isMale = summaryInfo.circumference > 140
  summaryInfo.recommendedGender = isMale ? '男性' : '女性'
  summaryInfo.gender = isMale ? 1 : 0 // 0:女 1:男

  // 计算总费用和珠子详情
  let totalBeadCost = 0
  const beadCountMap = {}

  selectedBeads.forEach((bead) => {
    if (bead && bead.price) {
      totalBeadCost += parseFloat(bead.price) || 0
    }
    // 统计珠子详情
    const materialName = bead.materialName || bead.name || '宝石'
    const code = bead.code || ''
    const size = bead.size || '8'
    const price = parseFloat(bead.price) || 0
    const skuId = bead.skuId
    const key = `${materialName}(${size}mm)_${price}` // 按材质、尺寸、价格分组

    if (!beadCountMap[key]) {
      beadCountMap[key] = {
        code, // 款号(SKU编码) - 用于详情展示
        name: `${materialName}(${size}mm)`,
        count: 0,
        unitPrice: price,
        skuIds: [], // 保存所有相关的 skuId
      }
    }
    beadCountMap[key].count++
    if (skuId && !beadCountMap[key].skuIds.includes(skuId)) {
      beadCountMap[key].skuIds.push(skuId)
    }
  })

  // 转换为数组格式
  summaryInfo.beadDetails = Object.values(beadCountMap)
  summaryInfo.totalCost = totalBeadCost + summaryInfo.handworkFee
}

// 重新计算所有珠子位置
const recalculateBeadPositions = () => {
  if (!canvasEl || !ctx) {
    console.error('Canvas未初始化，无法重新计算珠子位置')
    return
  }

  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y

  const size = Math.min(canvasEl.width, canvasEl.height)
  const referenceRadius = size * 0.35
  const gapAngle = state.gapAngle
  const baseBeadSize = referenceRadius * 0.18
  const beadInfoArray = []
  const actualBeadCount = state.beads.length
  if (actualBeadCount === 0) {
    return false
  }
  let maxBeadSize = 0
  for (let i = 0; i < actualBeadCount; i++) {
    const currentBead = state.beads[i]
    let beadSize = baseBeadSize

    if (currentBead) {
      if (currentBead.isEmptyHole && currentBead.originalSize) {
        const size = currentBead.originalSize
        if (size === 12) {
          beadSize = baseBeadSize * 1.0
        } else {
          const sizeRatio = size / 12
          beadSize = baseBeadSize * sizeRatio
        }
      } else if (currentBead.size) {
        if (currentBead.size === 12) {
          beadSize = baseBeadSize * 1.0
        } else {
          const sizeRatio = currentBead.size / 12
          beadSize = baseBeadSize * sizeRatio
        }
      }
    }

    maxBeadSize = Math.max(maxBeadSize, beadSize)

    beadInfoArray.push({
      index: i,
      beadSize,
      hasBead: !!(currentBead && !currentBead.isEmptyHole),
      bead: currentBead,
    })
  }

  function calculateTotalAngle(radius) {
    const safeRadius = Math.max(radius, maxBeadSize + 0.1)

    let totalAngle = 0
    for (const info of beadInfoArray) {
      if (info.beadSize >= safeRadius) {
        totalAngle += Math.PI
      } else {
        const ratio = info.beadSize / safeRadius
        const safeRatio = Math.min(Math.max(ratio, -1), 1)
        const beadAngle = 2 * Math.asin(safeRatio)
        totalAngle += beadAngle
      }
    }
    totalAngle += gapAngle * actualBeadCount
    return totalAngle
  }

  const minRequiredRadius = Math.max(
    maxBeadSize * 1.1,
    (beadInfoArray.reduce((sum, info) => sum + info.beadSize, 0) / (actualBeadCount * Math.PI)) * 2,
  )

  let minRadius = minRequiredRadius
  let maxRadius = referenceRadius * 2.0
  let idealRadius = referenceRadius
  const target = 2 * Math.PI
  const tolerance = 0.0001
  let iterations = 0

  if (idealRadius < minRadius) {
    idealRadius = minRadius
  }

  while (iterations < 20) {
    const totalAngle = calculateTotalAngle(idealRadius)
    const error = Math.abs(totalAngle - target)

    if (error < tolerance) {
      break
    }

    if (totalAngle > target) {
      minRadius = idealRadius
      idealRadius = (minRadius + maxRadius) / 2
    } else {
      maxRadius = idealRadius
      idealRadius = (minRadius + maxRadius) / 2
    }

    iterations++
  }

  idealRadius = Math.max(idealRadius, minRequiredRadius)
  const beadAngles = []
  let totalBeadAndGapAngle = 0

  for (let i = 0; i < beadInfoArray.length; i++) {
    const info = beadInfoArray[i]
    let beadAngle
    if (info.beadSize >= idealRadius) {
      beadAngle = Math.PI / 2
    } else {
      const ratio = info.beadSize / idealRadius
      const safeRatio = Math.min(Math.max(ratio, -1), 1)
      beadAngle = 2 * Math.asin(safeRatio)
    }

    beadAngles.push({
      index: i,
      beadAngle,
      gapAngle,
    })
    totalBeadAndGapAngle += beadAngle + gapAngle
  }
  const remainingAngle = Math.max(0, 2 * Math.PI - totalBeadAndGapAngle)
  const additionalGapAngle = actualBeadCount > 0 ? remainingAngle / actualBeadCount : 0
  const adjustedGapAngle = gapAngle + additionalGapAngle
  let currentAngle = -Math.PI / 2

  for (let i = 0; i < beadInfoArray.length; i++) {
    const info = beadInfoArray[i]
    const beadAngle = beadAngles[i].beadAngle
    const beadCenterAngle = currentAngle + beadAngle / 2
    const x = centerX + idealRadius * Math.cos(beadCenterAngle)
    const y = centerY + idealRadius * Math.sin(beadCenterAngle)
    info.centerX = x
    info.centerY = y
    info.centerAngle = beadCenterAngle
    info.beadAngle = beadAngle
    currentAngle += beadAngle + adjustedGapAngle
  }

  // 按比例缩放珠子尺寸和位置，保持手串整体大小不变
  if (idealRadius > referenceRadius) {
    const scaleRatio = referenceRadius / idealRadius
    idealRadius = referenceRadius
    beadInfoArray.forEach((info) => {
      info.beadSize *= scaleRatio
      info.centerX = centerX + (info.centerX - centerX) * scaleRatio
      info.centerY = centerY + (info.centerY - centerY) * scaleRatio
    })
  }

  const calcBeadAngle = (size) =>
    size >= idealRadius ? Math.PI / 2 : 2 * Math.asin(Math.min(Math.max(size / idealRadius, -1), 1))

  let totalAngleAfterScale = 0
  beadInfoArray.forEach((info) => {
    totalAngleAfterScale += calcBeadAngle(info.beadSize) + gapAngle
  })

  if (totalAngleAfterScale > 2 * Math.PI + 0.0001) {
    const shrinkRatio = (2 * Math.PI) / totalAngleAfterScale
    beadInfoArray.forEach((info) => {
      info.beadSize *= shrinkRatio
    })
  }

  // 重新计算角度及位置
  currentAngle = -Math.PI / 2
  beadInfoArray.forEach((info, idx) => {
    const beadAngleNow = calcBeadAngle(info.beadSize)
    const beadCenterAngle = currentAngle + beadAngleNow / 2
    info.centerAngle = beadCenterAngle
    info.beadAngle = beadAngleNow
    info.centerX = centerX + idealRadius * Math.cos(beadCenterAngle)
    info.centerY = centerY + idealRadius * Math.sin(beadCenterAngle)
    currentAngle += beadAngleNow + gapAngle
  })

  const finalAngle = currentAngle
  const expectedFinalAngle = -Math.PI / 2 + 2 * Math.PI
  const closingGap = Math.abs(finalAngle - expectedFinalAngle)
  const isClosedCircle = closingGap < 0.01
  const firstLastGapMatch = Math.abs(adjustedGapAngle - closingGap) < 0.01

  state.beadInfoArray = beadInfoArray
  state.actualRadius = idealRadius

  return isClosedCircle
}

const message = useMessage()

const showPopup = ref(false)
const closePopup = () => {
  showPopup.value = false
  if (canvasEl && ctx) {
    state.canvasClass = 'bracelet-canvas'
    smoothAnimate(originalState.scale, 0)
  }
}

const smoothAnimate = (targetScale, targetY) => {
  if (!ctx || !canvasEl) {
    console.error('Canvas上下文或元素不存在，无法执行动画')
    return
  }

  state.isInAnim = true

  if (state.beadDrawTaskTimer) {
    clearTimeout(state.beadDrawTaskTimer)
    state.beadDrawTaskTimer = null
  }

  if (state.animationTimer) {
    clearTimeout(state.animationTimer)
  }

  const startScale = state.scale
  const startY = state.translateY || 0
  const steps = 15
  const duration = 250
  const stepDuration = duration / steps

  let currentStep = 0

  const step = () => {
    if (!ctx || !canvasEl) {
      state.isInAnim = false
      console.error('Canvas上下文或元素在动画过程中丢失，停止动画')
      return
    }

    currentStep++

    const progress = currentStep / steps
    const easeProgress = 1 - Math.pow(1 - progress, 2)

    state.scale = startScale + (targetScale - startScale) * easeProgress
    state.translateY = startY + (targetY - startY) * easeProgress

    ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
    state.isProcessingDrawTasks = false

    try {
      drawBracelet()
    } catch (err) {
      state.isInAnim = false
      console.error('动画中绘制出错:', err)
      return
    }

    if (currentStep < steps) {
      state.animationTimer = setTimeout(step, stepDuration)
    } else {
      state.isInAnim = false
    }
  }

  step()
}

const drawBracelet = () => {
  if (!ctx) {
    console.error('Canvas上下文不存在')
    return
  }

  if (!canvasEl) {
    console.error('Canvas元素不存在')
    return
  }

  if (!state.beadInfoArray || state.beadInfoArray.length === 0) {
    if (state.beads.length <= 3 && state.beads.length > 0) {
      applySpecialLayout(state.beads.length)
    } else if (state.beads.length > 0) {
      recalculateBeadPositions()
    }
  }

  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)

  ctx.fillStyle = 'rgba(255, 255, 255, 0.0)'
  ctx.fillRect(0, 0, canvasEl.width, canvasEl.height)

  // 先绘制气泡
  drawSummaryBubbles()

  // 再绘制手串
  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y
  const radius = state.actualRadius || Math.min(canvasEl.width, canvasEl.height) * 0.35
  ctx.save()

  ctx.translate(centerX, centerY + state.translateY)
  ctx.rotate(state.rotation)
  ctx.scale(state.scale, state.scale)
  ctx.translate(-centerX, -centerY)

  drawBeadHoles(centerX, centerY, radius)

  console.log('手串绘制完成')
  ctx.restore()

  if (canvasEl.canvas && typeof canvasEl.canvas.requestAnimationFrame === 'function') {
    canvasEl.canvas.requestAnimationFrame(() => {
      console.log('强制帧刷新完成')
    })
  }
}

// 修改为接收文本数组的气泡绘制函数
const drawPriceBubbles = (textArray) => {
  if (!ctx || !canvasEl || !textArray || textArray.length === 0) return

  const padding = 10
  const bubbleHeight = 32
  const cornerRadius = 15
  const marginLeft = 5
  const marginTop = 5
  const bubbleGap = 5 // 气泡之间的间距

  // 设置字体样式
  ctx.font = '14px sans-serif'

  // 保存所有气泡的宽度
  const bubbleWidths = textArray.map((text) => ctx.measureText(text).width + padding * 2)

  // 绘制所有气泡
  ctx.save()

  textArray.forEach((text, index) => {
    const y = marginTop + index * (bubbleHeight + bubbleGap)

    // 为每个气泡单独设置样式
    ctx.save()
    ctx.fillStyle = '#ffffff'
    ctx.shadowColor = 'rgba(0, 0, 0, 0.1)'
    ctx.shadowBlur = 10
    ctx.shadowOffsetX = 0
    ctx.shadowOffsetY = 2

    // 绘制气泡背景
    drawBubble(marginLeft, y, bubbleWidths[index], bubbleHeight, cornerRadius)
    ctx.restore()

    // 绘制文本
    ctx.fillStyle = '#333333'
    ctx.textBaseline = 'middle'
    ctx.fillText(text, marginLeft + padding, y + bubbleHeight / 2)
  })

  ctx.restore()
}

// 生成宝石信息字符串
const generateBeadInfoText = () => {
  const selectedBeads = state.beads.filter((bead) => bead !== null && !bead.isEmptyHole)

  if (selectedBeads.length === 0) return ''

  // 如果有选中的珠子，显示选中的珠子信息
  if (
    state.selectedIndex !== -1 &&
    state.beads[state.selectedIndex] &&
    !state.beads[state.selectedIndex].isEmptyHole
  ) {
    const selectedBead = state.beads[state.selectedIndex]
    return `${selectedBead.materialName || selectedBead.name} ${selectedBead.size}mm ¥${selectedBead.price}`
  }

  // 如果只有1颗珠子，直接显示
  if (selectedBeads.length === 1) {
    const bead = selectedBeads[0]
    const materialName = bead.materialName || bead.name || '宝石'
    const size = bead.size || '8'
    const price = bead.price || '0.00'
    return `${materialName} ${size}mm ¥${price}`
  }

  // 如果有多颗珠子，显示数量最多的那种
  const beadCountMap = {}
  selectedBeads.forEach((bead) => {
    const key = `${bead.materialName || bead.name || '宝石'}_${bead.size || '8'}_${bead.price || '0.00'}`
    if (!beadCountMap[key]) {
      beadCountMap[key] = {
        bead,
        count: 0,
      }
    }
    beadCountMap[key].count++
  })

  // 找到数量最多的宝石
  let mostCommonBead = null
  let maxCount = 0
  Object.values(beadCountMap).forEach((item: any) => {
    if (item.count > maxCount) {
      maxCount = item.count
      mostCommonBead = item.bead
    }
  })

  if (mostCommonBead) {
    const materialName = mostCommonBead.materialName || mostCommonBead.name || '宝石'
    const size = mostCommonBead.size || '8'
    const price = mostCommonBead.price || '0.00'
    return `${materialName} ${size}mm ¥${price}`
  }

  return ''
}

// 绘制汇总信息气泡
const drawSummaryBubbles = () => {
  if (!ctx || !canvasEl || summaryInfo.totalCount === 0) return

  const beadInfoText = generateBeadInfoText()
  const textArray = [
    `${summaryInfo.totalCount}颗 周长${summaryInfo.circumference}mm 推荐${summaryInfo.recommendedGender}佩戴 ￥${summaryInfo.totalCost.toFixed(2)}`,
    beadInfoText,
  ]

  drawPriceBubbles(textArray)
}

// 辅助函数：绘制单个气泡（保持不变）
const drawBubble = (x, y, width, height, radius) => {
  ctx.beginPath()
  ctx.moveTo(x + radius, y)
  ctx.lineTo(x + width - radius, y)
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
  ctx.lineTo(x + width, y + height - radius)
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
  ctx.lineTo(x + radius, y + height)
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
  ctx.lineTo(x, y + radius)
  ctx.quadraticCurveTo(x, y, x + radius, y)
  ctx.closePath()
  ctx.fill()
}

const drawBeadHoles = (centerX, centerY, radius) => {
  console.log(
    '开始绘制珠子孔，中心:',
    centerX,
    centerY,
    '初始半径:',
    radius,
    state.beads.findIndex((b) => b === undefined),
  )

  const beadInfoArray = state.beadInfoArray || []
  if (beadInfoArray.length === 0 && state.beads.length > 0) {
    console.error('警告：有珠子数据但没有珠子位置信息')
  }

  if (state.beadDrawTaskTimer) {
    clearTimeout(state.beadDrawTaskTimer)
    state.beadDrawTaskTimer = null
  }

  const drawTasks = []
  const beadsToActuallyRemove = []

  for (const info of beadInfoArray) {
    const currentBead = state.beads[info.index]

    if (
      info.index >= state.totalBeads &&
      (!currentBead || (currentBead.isEmptyHole && !currentBead.removedBead))
    ) {
      console.log('跳过绘制', info.index)
      continue
    }

    let x = info.centerX
    let y = info.centerY

    if (info.prevX !== undefined && info.prevY !== undefined) {
      const mp = info.moveProgress !== undefined ? info.moveProgress : 0
      x = info.prevX + (info.centerX - info.prevX) * mp
      y = info.prevY + (info.centerY - info.prevY) * mp
      info.moveProgress = Math.min(mp + 0.08, 1)
      if (info.moveProgress < 1) {
        scheduleDraw()
      } else {
        delete info.prevX
        delete info.prevY
        delete info.moveProgress
      }
    }

    // 珠子存在 dropOffset，添加回落动画
    if (currentBead && currentBead.dropOffset && currentBead.dropOffset > 0) {
      y -= currentBead.dropOffset

      currentBead.dropOffset *= 0.9

      if (currentBead.dropOffset < 0.3) {
        currentBead.dropOffset = 0
      } else {
        scheduleDraw()
      }
    }

    const bgColor = '#f5f5f5'
    const beadSize = info.beadSize
    const isSelected = info.index === state.selectedIndex

    if (isSelected) {
      // 图片缓存及形状判断
      let cachedImg = null
      let shadowIsCircle = true

      if (currentBead && currentBead.imagePath) {
        const cacheKey = `${currentBead.imagePath}&uid=${currentBead.uniqueId || ''}&idx=${info.index}`
        cachedImg = beadImageCache[cacheKey]

        if (cachedImg) {
          shadowIsCircle = isCircularBead(cachedImg)
        } else {
          shadowIsCircle = false
        }
      }

      // 计算矩形尺寸
      let rectW = beadSize * 2
      let rectH = beadSize * 2
      if (!shadowIsCircle && cachedImg) {
        const circularHeight = beadSize * 2
        const scale = circularHeight / cachedImg.height
        rectW = cachedImg.width * scale
        rectH = circularHeight
        if (rectW > beadSize * 2) {
          const maxScale = (beadSize * 2) / cachedImg.width
          rectW = beadSize * 2
          rectH = cachedImg.height * maxScale
        }
      }

      const isSingleBead = beadInfoArray.length === 1
      let vectorX, vectorY

      if (isSingleBead) {
        vectorX = 0
        vectorY = 1
      } else {
        vectorX = centerX - x
        vectorY = centerY - y
      }

      const magnitude = Math.sqrt(vectorX * vectorX + vectorY * vectorY) || 1
      const normalizedX = vectorX / magnitude
      const normalizedY = vectorY / magnitude

      const shadowLayers = 6
      const maxShadowWidth = beadSize * 0.25

      for (let j = 0; j < shadowLayers; j++) {
        const layerAlpha = 0.35 - j * 0.05
        // const layerAlpha = 0.4 - j * 0.07 // 淡化梯度
        const expand = maxShadowWidth * (j / shadowLayers)
        const offsetRatio = j * 0.025
        const offsetX = x + normalizedX * offsetRatio * beadSize
        const offsetY = y + normalizedY * offsetRatio * beadSize

        ctx.beginPath()
        ctx.fillStyle = `rgba(244, 67, 54, ${layerAlpha})`

        if (shadowIsCircle) {
          ctx.beginPath()
          ctx.arc(offsetX, offsetY, beadSize + expand, 0, Math.PI * 2)
          ctx.fill()
        } else {
          const imgW = cachedImg.width || beadSize * 2
          const imgH = cachedImg.height || beadSize * 2
          const circularHeight = beadSize * 2 // 与圆形珠子直径一致
          const scale = circularHeight / imgH // 根据高度计算缩放比例
          let drawW = imgW * scale // 初步计算宽度
          let drawH = circularHeight

          if (drawW > beadSize * 2) {
            const maxScale = (beadSize * 2) / imgW
            drawW = beadSize * 2
            drawH = imgH * maxScale
          }
          if (drawW < beadSize * 2) {
            const minScale = (beadSize * 2) / imgW
            drawW = beadSize * 2
            drawH = imgH * minScale
          }
          ctx.save()
          ctx.translate(offsetX, offsetY)
          ctx.rotate(info.centerAngle + Math.PI / 2)
          const baseSize = Math.max(drawW, drawH)
          const shadowExpand = expand * 2
          const rectX = -drawW / 2 - shadowExpand
          const rectY = -drawH / 2 - shadowExpand
          const rectW = drawW + shadowExpand * 2
          const rectH = drawH + shadowExpand * 2
          const cornerR = Math.min(baseSize * 0.2 + shadowExpand, Math.min(rectW, rectH) / 2)
          drawRoundedRect(ctx, rectX, rectY, rectW, rectH, cornerR)
          ctx.restore()
        }
      }
    }

    if (currentBead && !currentBead.isEmptyHole) {
      const progress = currentBead.appearProgress !== undefined ? currentBead.appearProgress : 1
      let displaySize

      if (currentBead.isRemoving) {
        displaySize = beadSize * progress
        currentBead.appearProgress = Math.max(progress - 0.03, 0)
        if (currentBead.appearProgress > 0) {
          scheduleDraw()
        } else {
          // 动画结束后统一移除
          beadsToActuallyRemove.push(info.index)
          continue
        }
      } else {
        displaySize = beadSize * (progress < 1 ? progress : 1)
        if (progress < 1) {
          currentBead.appearProgress = Math.min(progress + 0.03, 1)
          scheduleDraw()
        }
      }

      // 只有当珠子没有图片时才绘制白色圆和珠子孔
      if (!currentBead.imagePath) {
        // 绘制白色透明圆
        ctx.beginPath()
        ctx.arc(x, y, displaySize * clipRatio, 0, Math.PI * 2)
        ctx.fillStyle = '#ffffff'
        ctx.fill()
      }

      // 只有当珠子没有图片时才绘制珠子孔
      if (!currentBead.imagePath) {
        // 珠子孔逐渐根据进度消失
        let holeProgress = 1
        if (currentBead && currentBead.isRemovingHole) {
          holeProgress = currentBead.appearProgress !== undefined ? currentBead.appearProgress : 1
          const nextProg = Math.max(holeProgress - 0.03, 0)
          currentBead.appearProgress = nextProg
          if (nextProg > 0) {
            scheduleDraw()
          } else {
            beadsToActuallyRemove.push(info.index)
            continue
          }
        }

        const effectiveRadius = beadSize * holeClipRatio * holeProgress

        ctx.save()
        ctx.globalAlpha = holeProgress
        ctx.beginPath()
        ctx.arc(x, y, effectiveRadius, 0, Math.PI * 2)
        ctx.fillStyle = isSelected ? '#ffffff' : bgColor
        ctx.fill()

        ctx.beginPath()
        // ctx.lineWidth = 1
        ctx.arc(x, y, effectiveRadius, 0, Math.PI * 2)
        ctx.strokeStyle = isSelected ? '#f44336' : '#dddddd'
        ctx.stroke()
        ctx.restore()
      }

      if (currentBead.imagePath && canvasEl && canvasEl.canvas && displaySize > beadSize * 0.3) {
        drawTasks.push({
          type: 'bead',
          x,
          y,
          beadSize: displaySize,
          isSelected,
          bead: currentBead,
          index: info.index,
          centerAngle: info.centerAngle, // 传递珠子中心角度
        })
      } else {
        // 图片还没加载时，如果珠子没有图片路径，则画个白色透明色圆
        if (!currentBead.imagePath) {
          ctx.beginPath()
          ctx.arc(x, y, displaySize * clipRatio, 0, Math.PI * 2)
          ctx.fillStyle = '#f0f0f0'
          ctx.fill()
        }

        if (isSelected) {
          ctx.beginPath()
          // ctx.lineWidth = 1.5
          ctx.arc(x, y, beadSize, 0, Math.PI * 2)
          ctx.strokeStyle = '#f44336'
          ctx.stroke()
        }
      }
    } else {
      ctx.beginPath()
      ctx.arc(x, y, beadSize * holeClipRatio, 0, Math.PI * 2)
      ctx.fillStyle = isSelected ? '#ffffff' : bgColor
      ctx.fill()

      ctx.beginPath()
      // ctx.lineWidth = 1
      ctx.arc(x, y, beadSize * holeClipRatio, 0, Math.PI * 2)
      ctx.strokeStyle = isSelected ? '#f44336' : '#dddddd'
      ctx.stroke()
    }
  }

  if (beadInfoArray.length === 0) {
    ctx.font = '14px sans-serif'
    ctx.fillStyle = '#999999'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText('请从下方选择珠子添加', centerX, centerY - 20)
    ctx.fillText('或点击添加按钮开始', centerX, centerY + 20)
  }

  if (drawTasks.length > 0) {
    state.isProcessingDrawTasks = true

    const transform = {
      centerX,
      centerY,
      translateY: state.translateY,
      rotation: state.rotation,
      scale: state.scale,
    }

    const allTasksCompleted = () => {
      state.isProcessingDrawTasks = false

      if (canvasEl.canvas && typeof canvasEl.canvas.requestAnimationFrame === 'function') {
        canvasEl.canvas.requestAnimationFrame(() => {
          console.log('所有珠子绘制任务完成')
        })
      }
    }

    let tasksCompleted = 0
    const totalTasks = drawTasks.length

    const taskCompleted = () => {
      tasksCompleted++
      if (tasksCompleted >= totalTasks) {
        allTasksCompleted()
      }
    }

    const taskPromises = drawTasks.map((task) => {
      return new Promise<void>((resolve) => {
        if (task.type === 'bead') {
          const uniqueUrl = `${task.bead.imagePath}&uid=${task.bead.uniqueId || Date.now()}&idx=${task.index}`

          if (beadImageCache[uniqueUrl]) {
            drawImageWithClipping(
              beadImageCache[uniqueUrl],
              task.x,
              task.y,
              task.beadSize,
              () => {
                resolve()
              },
              transform,
              task.centerAngle + Math.PI / 2,
              false,
              task.isSelected,
            )
          } else {
            loadBeadImage(uniqueUrl, (img) => {
              if (img) {
                drawImageWithClipping(
                  img,
                  task.x,
                  task.y,
                  task.beadSize,
                  () => {
                    resolve()
                  },
                  transform,
                  task.centerAngle + Math.PI / 2,
                  false,
                  task.isSelected,
                )
              } else {
                resolve()
              }
            })
          }
        } else {
          resolve()
        }
      })
    })

    // 等待所有任务完成
    Promise.all(taskPromises)
      .then(() => {
        taskCompleted()
      })
      .catch((err) => {
        console.error('绘制任务出错:', err)
        state.isProcessingDrawTasks = false
      })
  }

  // 已经有珠子动画结束，直接移除
  if (beadsToActuallyRemove.length > 0) {
    const hasHoles = state.beads.some((b) => b === null || (b && b.isEmptyHole))
    if (hasHoles) {
      state.beads.forEach((b, idx) => {
        if (b === null) {
          state.beads[idx] = { isEmptyHole: true, isRemovingHole: true, appearProgress: 1 }
        } else if (b && b.isEmptyHole && !b.isRemovingHole) {
          b.isRemovingHole = true
          b.appearProgress = b.appearProgress !== undefined ? b.appearProgress : 1
        }
      })
      scheduleDraw()
      return
    }

    const oldPosMap = {}
    state.beadInfoArray.forEach((info) => {
      oldPosMap[info.index] = { x: info.centerX, y: info.centerY }
    })

    beadsToActuallyRemove
      .sort((a, b) => b - a)
      .forEach((idx) => {
        state.beads.splice(idx, 1)
      })

    state.selectedIndex = -1
    state.beadInfoArray = []

    if (state.beads.length === 0) {
      initBeads()
    } else if (state.beads.length <= 3) {
      applySpecialLayout(state.beads.length)
    } else {
      recalculateBeadPositions()
    }

    state.beadInfoArray.forEach((info) => {
      const prev = oldPosMap[info.index]
      if (prev) {
        info.prevX = prev.x
        info.prevY = prev.y
        info.moveProgress = 0
      }
    })

    // 更新下一个添加位置
    state.nextAddPosition = state.beads.length < state.maxBeads ? state.beads.length : -1

    // 更新汇总信息
    updateSummaryInfo()

    // 平滑过渡到新的布局
    smoothAnimate(state.scale, state.translateY)
  }
}

// 绘制选中边框
function drawSelectedBorder(x, y, beadSize, transform) {
  const clipRatio = 1 // 保持与 drawBeadHoles 一致
  ctx.save()
  ctx.beginPath()
  // ctx.lineWidth = 1.5
  ctx.arc(x, y, beadSize * clipRatio, 0, Math.PI * 2)
  ctx.strokeStyle = '#f44336'
  ctx.stroke()
  ctx.restore()
}

// 让珠子图片根据中心角度旋转，使孔道朝向手串中心
function drawImageWithClipping(
  image,
  x,
  y,
  beadSize,
  callback,
  transform,
  rotationAngle = 0,
  needTransform = true,
  isSelected = false,
) {
  if (!image) {
    if (callback) callback()
    return
  }

  try {
    ctx.save()

    if (needTransform && transform) {
      const { centerX, centerY, translateY, rotation, scale } = transform
      ctx.translate(centerX, centerY + translateY)
      ctx.rotate(rotation)
      ctx.scale(scale, scale)
      ctx.translate(-centerX, -centerY)
    }

    // 将原点移至珠子中心并旋转到指定角度
    ctx.translate(x, y)
    ctx.rotate(rotationAngle)

    // 根据图片类型调整绘制方式
    const imgW = image.width || beadSize * 2
    const imgH = image.height || beadSize * 2

    let drawW, drawH
    if (isCircularBead(image)) {
      // 圆形珠子：保持现在的效果，最长边等于 beadSize*2
      const maxSide = beadSize * 2
      const scale = Math.min(maxSide / imgW, maxSide / imgH)
      drawW = imgW * scale
      drawH = imgH * scale
    } else {
      // 长方形珠子：高度保持和圆形一致，宽度按比例缩放且不超过 beadSize*2，确保间距一致
      const circularHeight = beadSize * 2 // 与圆形珠子直径一致
      const scale = circularHeight / imgH // 根据高度计算缩放比例

      drawW = imgW * scale // 初步计算宽度
      drawH = circularHeight

      if (drawW > beadSize * 2) {
        const maxScale = (beadSize * 2) / imgW
        drawW = beadSize * 2
        drawH = imgH * maxScale
      }
      if (drawW < beadSize * 2) {
        const minScale = (beadSize * 2) / imgW
        drawW = beadSize * 2
        drawH = imgH * minScale
      }
    }

    ctx.drawImage(image, -drawW / 2, -drawH / 2, drawW, drawH)

    // 长方形红色边框
    // if (isSelected && !isCircularBead(image)) {
    //   ctx.beginPath()
    //   ctx.lineWidth = 1.5
    //   ctx.strokeStyle = '#f44336'
    //   ctx.strokeRect(-drawW / 2, -drawH / 2, drawW, drawH)
    // }

    // 正方形红色边框
    if (isSelected && isCircularBead(image)) {
      ctx.beginPath()
      // ctx.lineWidth = 1.5
      ctx.strokeStyle = '#f44336'
      ctx.arc(0, 0, Math.max(drawW, drawH) / 2, 0, Math.PI * 2)
      ctx.stroke()
    }

    // 还原至调用前状态
    ctx.restore()

    if (callback) callback()
  } catch (e) {
    console.error('绘制图片出错:', e)
    if (ctx) ctx.restore()
    if (callback) callback()
  }
}

// 组件作用域增加图片缓存对象
const beadImageCache = {}

// 图片加载并缓存
function loadBeadImage(url, onload) {
  if (beadImageCache[url]) {
    onload(beadImageCache[url])
    return
  }
  const image = canvasEl.canvas.createImage()
  image.onload = () => {
    beadImageCache[url] = image
    onload(image)
  }
  image.onerror = (err) => {
    console.error('图片加载失败:', url, err)
    onload(null)
  }
  image.src = url
}

function onGemDetail(id: string) {
  if (!checkLogin()) return
  uni.navigateTo({ url: `/pages/gemLibrary/detail?id=${id}` })
}

// 保存定制
const onComplete = () => {
  cropBeads(false) // 最后自动裁剪珠子

  updateSummaryInfo() // 更新汇总信息

  console.log('当前手串数据:', JSON.stringify(state.beads))
  console.log('汇总信息:', {
    totalCount: summaryInfo.totalCount,
    circumference: summaryInfo.circumference,
    recommendedGender: summaryInfo.recommendedGender,
    gender: summaryInfo.gender,
    totalCost: summaryInfo.totalCost,
    handworkFee: summaryInfo.handworkFee,
    beadDetails: summaryInfo.beadDetails,
  })

  const { setData } = useData()
  setData('zydz', {
    beads: state.beads,
    summaryInfo: {
      ...summaryInfo,
      totalCost: summaryInfo.totalCost.toFixed(2),
    },
  })
  uni.navigateTo({
    url: `/pages/customizationDetails/index`,
  })
}

// 计算手串的边界框
const calculateBraceletBoundingBox = () => {
  if (!state.beadInfoArray || state.beadInfoArray.length === 0) {
    return null
  }
  let minX = Infinity
  let minY = Infinity
  let maxX = -Infinity
  let maxY = -Infinity
  state.beadInfoArray.forEach((info) => {
    const x = info.centerX
    const y = info.centerY
    const beadSize = info.beadSize
    minX = Math.min(minX, x - beadSize)
    minY = Math.min(minY, y - beadSize)
    maxX = Math.max(maxX, x + beadSize)
    maxY = Math.max(maxY, y + beadSize)
  })
  // 变换四角
  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y
  const corners = [
    { x: minX, y: minY },
    { x: maxX, y: minY },
    { x: maxX, y: maxY },
    { x: minX, y: maxY },
  ]
  let transformedMinX = Infinity
  let transformedMinY = Infinity
  let transformedMaxX = -Infinity
  let transformedMaxY = -Infinity
  corners.forEach((corner) => {
    const relX = corner.x - centerX
    const relY = corner.y - centerY
    const cos = Math.cos(state.rotation)
    const sin = Math.sin(state.rotation)
    const rotatedX = relX * cos - relY * sin
    const rotatedY = relX * sin + relY * cos
    const transformedX = rotatedX * state.scale + centerX
    const transformedY = rotatedY * state.scale + centerY + state.translateY
    transformedMinX = Math.min(transformedMinX, transformedX)
    transformedMinY = Math.min(transformedMinY, transformedY)
    transformedMaxX = Math.max(transformedMaxX, transformedX)
    transformedMaxY = Math.max(transformedMaxY, transformedY)
  })
  return {
    left: transformedMinX,
    top: transformedMinY,
    right: transformedMaxX,
    bottom: transformedMaxY,
    width: transformedMaxX - transformedMinX,
    height: transformedMaxY - transformedMinY,
  }
}

// 节流绘制 - 避免高频刷新导致图片闪烁
let frameRequested = false
const scheduleDraw = () => {
  if (frameRequested) return
  frameRequested = true
  const raf =
    canvasEl && canvasEl.canvas && typeof canvasEl.canvas.requestAnimationFrame === 'function'
      ? canvasEl.canvas.requestAnimationFrame.bind(canvasEl.canvas)
      : typeof requestAnimationFrame === 'function'
        ? requestAnimationFrame
        : (cb) => setTimeout(cb, 16)
  raf(() => {
    frameRequested = false
    drawBracelet()
  })
}

// 引导演示相关状态
const showGuideDemoFlag = ref(false)

// 显示引导演示
const showDemoGuide = () => {
  showGuideDemoFlag.value = true
}

// 关闭引导演示
const closeGuideDemoHandler = () => {
  showGuideDemoFlag.value = false
}

// 长方形珠子阴影效果保持和圆形一致
const drawRoundedRect = (
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number,
  radius: number,
) => {
  const r = Math.max(0, Math.min(radius, Math.min(width, height) / 2))
  ctx.beginPath()
  ctx.moveTo(x + r, y)
  ctx.lineTo(x + width - r, y)
  ctx.quadraticCurveTo(x + width, y, x + width, y + r)
  ctx.lineTo(x + width, y + height - r)
  ctx.quadraticCurveTo(x + width, y + height, x + width - r, y + height)
  ctx.lineTo(x + r, y + height)
  ctx.quadraticCurveTo(x, y + height, x, y + height - r)
  ctx.lineTo(x, y + r)
  ctx.quadraticCurveTo(x, y, x + r, y)
  ctx.closePath()
  ctx.fill()
}
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.canvas-container {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 60vh;
  max-height: 60vh;
  padding-bottom: 0;
  overflow: hidden;
  overscroll-behavior: contain;
  touch-action: none;
  background: white;
  scroll-behavior: smooth;
}

.bracelet-canvas {
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  display: block;
  width: 100% !important;
  height: 100% !important;
  aspect-ratio: unset;
  touch-action: none;
  background: transparent;
}
/* 网格背景 */
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to right, #e0e0e0 1rpx, transparent 1rpx),
    linear-gradient(to bottom, #e0e0e0 1rpx, transparent 1rpx);
  background-size: 20rpx 20rpx;
  opacity: 0.6;
}
/* 底部容器 */
.bottom-container {
  position: relative;
  z-index: 5;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 40vh;
  max-height: 40vh;
  padding: 0 20rpx 20rpx 20rpx;
  overflow-y: auto;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}
/* 悬浮的新手引导按钮 */
.floating-guide-button {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 12rpx;
  background-color: rgba(248, 249, 250, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}
/* 引导图标 */
.guide-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  // border-radius: 12rpx;
  margin-bottom: 8rpx;
  background-color: #ffffff;
}

.guide-icon-image {
  width: 32rpx;
  height: 32rpx;
  background-color: #fff;
}
/* 引导文字 */
.guide-text {
  font-size: 18rpx;
  font-weight: 500;
}
/* 操作区域 */
.action-area {
  // border-bottom: 1rpx solid #eee;
  // background-color: white;
  padding: 8rpx;
  padding-bottom: 10rpx;
}

.action-buttons-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.action-buttons {
  display: flex;
  gap: 10rpx;
}
/* Tabs区域 */
.tabs-area {
  background-color: white;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  border-bottom: 1rpx solid #eee;
}

.header h1 {
  flex: 1;
  font-size: 18rpx;
  font-weight: normal;
  text-align: center;
}

.back-btn {
  display: flex;
  align-items: center;
  padding: 0 8rpx;
  font-size: 24rpx;
  background: none;
  border: none;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.header-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24rpx;
  height: 24rpx;
  background: none;
  border: none;
}

.product-info {
  padding: 12rpx 16rpx;
  font-size: 14rpx;
  line-height: 1.8;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.action-btn {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 36rpx;
  font-size: 16rpx;
  background: none;
  border: none;
}

.action-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
  opacity: 0.4;
}

.complete-btn {
  display: flex;
  flex: 1.2;
  gap: 6rpx;
  align-items: center;
  justify-content: center;
  height: 36rpx;
  color: white;
  background: #026e6f;
  border-radius: 4rpx;
}

.tab-container {
  border-bottom: 1rpx solid #eee;
}

.tabs {
  display: flex;
  padding: 8rpx 16rpx;
  overflow-x: auto;
  scrollbar-width: none;
}

.tabs::-webkit-scrollbar {
  display: none;
}

.tab {
  padding: 12rpx 16rpx;
  white-space: nowrap;
  background: none;
  border: none;
  border-bottom: 2rpx solid transparent;
}

.tab.active {
  color: #026e6f;
  border-bottom-color: #026e6f;
}

.element-filter {
  display: flex;
  gap: 8rpx;
  justify-content: center;
  padding: 12rpx;
}

.element-btn {
  padding: 6rpx 12rpx;
  background: white;
  border: 1rpx solid #ddd;
  border-radius: 15rpx;
}

.bead-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  justify-content: center;
  padding: 16rpx;
}

.bead-thumbnail {
  width: 56rpx;
  height: 56rpx;
  overflow: hidden;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.bead-thumbnail img {
  width: 100%;
  height: 100%;
  cursor: pointer;
  object-fit: cover;
}

.action-btn svg {
  width: 24rpx;
  height: 24rpx;
}

canvas {
  touch-action: none;
}

.bead-info-section {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10rpx;
  margin-top: 35rpx;
  margin-bottom: 45rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  box-shadow: none;
}

.bead-avatar-container {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  padding: 25rpx;
  margin-right: 30rpx;
  overflow: hidden;
  background-color: #fff;
  // border-image: url('/static/about/avtar.svg') 8 repeat;
  background-image: url('/static/about/avtar.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-clip: border-box;
  background-size: 100% 100%;
  border: 8rpx solid transparent;
  border-radius: 12rpx;
}

.bead-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4rpx;
  /* Slightly rounded corners for image inside */
}

.bead-details {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 30rpx;
  justify-content: center;
}

.bead-name {
  margin-bottom: 0;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.bead-material {
  font-size: 27rpx;
  color: #888;
}

.detail-link {
  position: absolute;
  top: 45rpx;
  right: -15rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  background-color: #f5f5f5;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.size-price-list {
  display: flex;
  flex-direction: column;
  gap: 50rpx;
  padding-left: 40rpx;
}

.size-price-item {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 96rpx;
  padding: 0 24rpx;
  margin-bottom: 0;
  background-color: #ffffff;
  border: 2rpx solid transparent;
  border-radius: 20rpx;
  box-shadow: none;
  transition: all 0.2s;
}

.size-name {
  font-size: 28rpx;
  font-weight: bold;
}

.price {
  font-size: 26rpx;
  color: #555;
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 43rpx;
  height: 43rpx;
  background-color: #00a989;
  border-radius: 50%;
  transform: translateY(-50%);
}

.add-icon {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
}

.back-icon {
  font-size: 32rpx;
  font-weight: bold;
}
:deep(.wd-picker-view) {
  padding: 70rpx 0rpx !important;
}
:deep(.wd-icon-close) {
  font-size: 28rpx !important;
}

:deep(.wd-picker-view-column__item--active) {
  color: #fc3b3b;
}

:deep(.wd-col-picker__selected-line) {
  background: #fc3b3b !important;
}
:deep(.wd-overlay) {
  background: transparent !important;
}
</style>
