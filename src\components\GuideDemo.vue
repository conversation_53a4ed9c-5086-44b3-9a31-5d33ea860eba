<template>
  <view class="guide-demo-container">
    <view class="canvas-container">
      <!-- 预览信息标签 -->
      <view v-if="beadList.length" class="preview-info">
        <view class="info-tag">{{ previewSummary }}</view>
        <view class="info-tag" v-if="lastBeadDetail">{{ lastBeadDetail }}</view>
      </view>
      <!-- 网格背景辅助线 -->
      <view class="grid-background"></view>
      <canvas
        canvas-id="guideCanvas"
        id="guideCanvas"
        type="2d"
        :class="state.canvasClass"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      ></canvas>
    </view>

    <!-- 底部操作区域 -->
    <view class="bottom-container">
      <!-- 操作按钮区域 -->
      <view class="action-area">
        <view class="action-buttons-container">
          <view
            class="action-buttons-container action-buttons-left"
            style="flex: 1; margin-right: 10rpx"
          >
            <view class="action-buttons">
              <wd-button
                custom-class="!bg-#fff shadow-md !text-40rpx !border-rd-15rpx !opacity-100"
                type="icon"
                :round="false"
                icon="arrow-left"
                @click="selectPrevBead"
                :disabled="state.selectedIndex === -1"
              ></wd-button>
              <wd-button
                custom-class="!bg-#fff shadow-md !text-40rpx !border-rd-15rpx !opacity-100"
                type="icon"
                :round="false"
                icon="arrow-right"
                @click="selectNextBead"
                :disabled="state.selectedIndex === -1"
              ></wd-button>
              <wd-button
                custom-class="!bg-#fff shadow-md !text-40rpx !border-rd-15rpx !opacity-100"
                type="icon"
                :round="false"
                icon="close-normal"
                @click="clearSelection"
                :disabled="state.selectedIndex === -1"
              ></wd-button>
            </view>
            <view>
              <wd-button
                custom-class="!bg-#fff shadow-md !border-rd-15rpx font-bold !opacity-100"
                type="info"
                :round="false"
                @click="cropBeads"
              >
                裁剪
              </wd-button>
            </view>
          </view>
          <view id="completeBtnWrapper">
            <wd-button
              id="completeBtn"
              type="primary"
              custom-class="shadow-md !border-rd-15rpx"
              :round="false"
              @click="onComplete"
            >
              完成
            </wd-button>
          </view>
        </view>
      </view>

      <!-- 珠子选择区域（使用原 Tabs 组件） -->
      <view class="tabs-area">
        <myWdTabs
          v-model="mainTab"
          size="large"
          color="#e63b3c"
          lineWidth="50rpx"
          :animated="false"
          slidable="always"
        >
          <!-- 仅示例"选五行"一类，其余类可按需扩展 -->
          <myWdTab title="选五行">
            <myWdTabs
              v-model="subTab"
              size="large"
              indicator="bg"
              color="#e63b3c"
              :tabsBg="getImagesUrl('685f7b76e4b00e5b211a8ce9.png')"
              :tabActiveBg="getImagesUrl('685f7b82e4b00e5b211a8cea.png')"
              tabWidth="121rpx"
              tabsHeight="86rpx"
              tabHeight="88rpx"
              :animated="false"
            >
              <block v-for="item in fiveElements" :key="item">
                <myWdTab :title="item">
                  <!-- 珠子列表 -->
                  <scroll-view scroll-x>
                    <view class="w100% flex gap-50rpx rpx-20rpx pt-10rpx">
                      <view
                        v-for="bead in demoBeads"
                        class="min-w120rpx flex flex-col gap-20rpx justify-center items-center"
                        :key="bead.id"
                        @click="showBeadInfo(bead)"
                      >
                        <wd-img :width="60" :height="60" :src="bead.image" round />
                        <view class="text-28rpx whitespace-nowrap">{{ bead.name }}</view>
                      </view>
                    </view>
                  </scroll-view>
                </myWdTab>
              </block>
            </myWdTabs>
          </myWdTab>
        </myWdTabs>
      </view>
    </view>

    <wd-gap safe-area-bottom height="0"></wd-gap>

    <!-- 引导遮罩和提示 -->
    <view v-if="showGuideMask" class="guide-mask" @click="closeGuide">
      <!-- 镂空高亮效果 -->
      <view
        v-if="highlightPosition"
        class="highlight-cutout"
        :style="{
          '--highlight-left': highlightPosition.left + 'px',
          '--highlight-top': highlightPosition.top + 'px',
          '--highlight-width': highlightPosition.width + 'px',
          '--highlight-height': highlightPosition.height + 'px',
        }"
      ></view>

      <!-- 高亮边框 -->
      <view
        v-if="highlightPosition"
        class="highlight-border"
        :style="{
          left: highlightPosition.left + 'px',
          top: highlightPosition.top + 'px',
          width: highlightPosition.width + 'px',
          height: highlightPosition.height + 'px',
        }"
      ></view>

      <!-- 引导提示框 -->
      <view
        v-if="tooltipPosition && currentGuideStep"
        :class="['guide-tooltip', tooltipPosition.direction]"
        :style="{
          left: tooltipPosition.left + 'px',
          top: tooltipPosition.top + 'px',
        }"
      >
        <!-- 箭头 -->
        <view class="tooltip-arrow" :class="tooltipPosition.direction"></view>

        <!-- 提示内容 -->
        <view class="tooltip-text">{{ currentGuideStep.text }}</view>

        <!-- 下一步按钮 -->
        <view class="tooltip-button" @click.stop="nextGuideStep">
          {{ currentStepIndex < guideSteps.length - 1 ? '知道了' : '完成' }}
        </view>
      </view>
    </view>

    <!-- 珠子详情弹窗 -->
    <wd-popup
      id="skuPopup"
      v-model="showPopup"
      custom-style="border-radius:32rpx;padding-left: 20rpx;padding-right: 20rpx;z-index:100002"
      safe-area-inset-bottom
      position="bottom"
      @close="closePopup"
    >
      <view v-if="beadInfo" class="mb-60rpx">
        <!-- 珠子基本信息 -->
        <view class="bead-info-section">
          <view class="bead-avatar-container">
            <image class="bead-avatar-image" :src="beadInfo?.image" />
          </view>
          <view class="bead-details">
            <view class="bead-name">{{ beadInfo?.name }}</view>
            <view class="bead-material">材质：{{ beadInfo?.materialName }}</view>
          </view>
          <wd-button
            type="info"
            custom-class="!min-w-50rpx !w150rpx rounded-15rpx!"
            @click="onGemDetail"
          >
            <span class="mr-10rpx">详情</span>
            <wd-icon name="arrow-right" size="28rpx" />
          </wd-button>
        </view>

        <!-- 尺寸和价格选择 -->
        <view class="size-price-list">
          <view id="addButtonTopArea">
            <view class="size-price-item">
              <view class="col-size size-name">{{ beadInfo?.skuList?.[0]?.diameter + 'mm' }}</view>
              <view class="col-price price">单价:￥{{ beadInfo?.skuList?.[0]?.price }}</view>
              <view class="col-icon">
                <wd-icon
                  name="add-circle"
                  size="40rpx"
                  color="#e73c3c"
                  @click="addToBracket(beadInfo, beadInfo?.skuList?.[0])"
                />
              </view>
            </view>
            <view class="size-price-item">
              <view class="col-size size-name">{{ beadInfo?.skuList?.[1]?.diameter + 'mm' }}</view>
              <view class="col-price price">单价:￥{{ beadInfo?.skuList?.[1]?.price }}</view>
              <view class="col-icon">
                <wd-icon
                  name="add-circle"
                  size="40rpx"
                  color="#e73c3c"
                  @click="addToBracket(beadInfo, beadInfo?.skuList?.[1])"
                />
              </view>
            </view>
          </view>
          <view class="size-price-item">
            <view class="col-size size-name">{{ beadInfo?.skuList?.[2]?.diameter + 'mm' }}</view>
            <view class="col-price price">单价:￥{{ beadInfo?.skuList?.[2]?.price }}</view>
            <view class="col-icon">
              <wd-icon
                name="add-circle"
                size="40rpx"
                color="#e73c3c"
                @click="addToBracket(beadInfo, beadInfo?.skuList?.[2])"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view id="popupBtns" class="w100% flex gap-20rpx">
        <wd-button
          custom-class="!min-w-50rpx w150rpx  rounded-15rpx!"
          type="info"
          icon="rollback"
          @click="cropBeads"
        ></wd-button>
        <view id="popupBtnsRight" class="flex-1">
          <wd-button custom-class="w100% rounded-15rpx!" type="primary" @click="closePopup">
            完成
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import {
  reactive,
  ref,
  computed,
  onMounted,
  onBeforeUnmount,
  nextTick,
  getCurrentInstance,
} from 'vue'
import myWdTabs from '@/components/wd-tabs/my-wd-tabs.vue'
import myWdTab from '@/components/wd-tabs/wd-tab/my-wd-tab.vue'
import { getImagesUrl } from '@/utils/getImagesUrl'

const instance = getCurrentInstance()

let canvasEl = null
let ctx = null

const state = reactive({
  beadSize: 20, // 珠子大小
  totalBeads: 18, // 初始珠子孔位数量
  maxBeads: 40, // 最大珠子数量
  selectedBeads: 4, // 初始选中的珠子数量
  selectedIndex: -1, // 当前选中的珠子索引 (-1 表示未选中)
  beads: [], // 珠子数组，null 表示空位
  gapAngle: (Math.PI / 180) * 0.3, // 珠子间的间隔角度
  touchPosition: null, // 存储最近一次触摸的位置
  scale: 1, // 缩放比例
  rotation: 0, // 旋转角度（弧度）
  lastTouches: [],
  transformOrigin: { x: 150, y: 150 },
  canvasReady: false,
  animationTimer: null,
  translateY: 0,
  isInAnim: false,
  moveAnima: null,
  canvasClass: 'bracelet-canvas',
  nextAddPosition: -1, // 下一个添加珠子的位置索引
  beadInfoArray: [], // 存储珠子信息
  actualRadius: null, // 保存实际使用的半径
  isProcessingDrawTasks: false, // 标记是否正在处理绘制任务
  beadDrawTaskTimer: null,
})

// 引导相关状态
const showGuideMask = ref(false)
const currentStepIndex = ref(0)
const tooltipPosition = ref(null)
const highlightPosition = ref(null)
const hasShownGuide = ref(false)

// 弹窗相关状态
const showPopup = ref(false)
const beadInfo = ref(null)

// 演示珠子数据
const demoBeads = ref([
  {
    id: 'demo-1',
    name: '玛瑙珠',
    image: 'https://cl.iqei.cn/file/gem/2025/6/24/685a669de4b00e5b2cf36198.jpg',
    materialName: '天然玛瑙',
    skuList: [
      { id: 'sku-1', diameter: 8, price: '15.00' },
      { id: 'sku-2', diameter: 10, price: '20.00' },
      { id: 'sku-3', diameter: 12, price: '25.00' },
    ],
  },
  {
    id: 'demo-2',
    name: '水晶珠',
    image: 'https://cl.iqei.cn/file/gem/2025/6/24/685a669de4b00e5b2cf36198.jpg',
    materialName: '天然水晶',
    skuList: [
      { id: 'sku-4', diameter: 8, price: '18.00' },
      { id: 'sku-5', diameter: 10, price: '28.00' },
      { id: 'sku-6', diameter: 12, price: '35.00' },
    ],
  },
  {
    id: 'demo-3',
    name: '黄金珠',
    image: 'https://cl.iqei.cn/file/gem/2025/6/24/685a669de4b00e5b2cf36198.jpg',
    materialName: '足金999',
    skuList: [
      { id: 'sku-7', diameter: 6, price: '88.00' },
      { id: 'sku-8', diameter: 8, price: '128.00' },
      { id: 'sku-9', diameter: 10, price: '188.00' },
    ],
  },
  {
    id: 'demo-4',
    name: '翡翠珠',
    image: 'https://cl.iqei.cn/file/gem/2025/6/24/685a669de4b00e5b2cf36198.jpg',
    materialName: '天然翡翠',
    skuList: [
      { id: 'sku-10', diameter: 8, price: '68.00' },
      { id: 'sku-11', diameter: 10, price: '98.00' },
      { id: 'sku-12', diameter: 12, price: '158.00' },
    ],
  },
])

// 引导步骤配置
const guideSteps = [
  {
    text: '这里的空位可以摆放宝石',
    target: 'canvas',
    type: 'canvas-area',
  },
  {
    text: '这里可以按照五行、结合色系选择宝石，也可以选择珠型配饰～',
    target: 'bead-selection',
    type: 'bead-selection',
  },
  {
    text: '此处可选择宝石不同的周长，点击+，您就选中了这颗宝石，上面就会有展示啦~',
    target: 'add-button',
    type: 'add-button',
  },
  {
    text: '如果不喜欢，点击左侧返回，选择完这颗宝石，点击完成，就可以继续选择其他宝石~',
    target: 'popup-bottom-buttons',
    type: 'popup-bottom-buttons',
  },
  {
    text: '如果需要调整，您还可以选择其他规格，再点击完成或返回~',
    target: 'popup-bottom-add-buttons',
    type: 'popup-bottom-add-buttons',
  },
  {
    text: '上方会根据你选择的宝石，实时刷新手链的样式，展示珠石颗数、手链周长、推荐佩戴性别~',
    target: 'bracelet-preview',
    type: 'bracelet-preview',
  },
  {
    text: '您可以选中某一颗宝石后，点击<>，调整宝石的位置，点击X，删除这颗宝石，点击"裁剪"，删除空余位置',
    target: 'action-buttons',
    type: 'action-buttons',
  },
  {
    text: '手串定制好啦！点击"完成"按钮就可以保存设计啦！',
    target: 'complete-button',
    type: 'complete-button',
  },
]

// 计算当前引导步骤
const currentGuideStep = computed(() => {
  return guideSteps[currentStepIndex.value]
})

// 创建初始数据
const initDemoData = () => {
  // 初始状态只有空位
  state.beads = Array.from({ length: state.totalBeads }, () => null)
  state.nextAddPosition = 0
  state.beadInfoArray = []
  state.actualRadius = null
}

// 触摸事件处理函数
const handleTouchStart = (event) => {
  if (event.touches && event.touches.length === 1) {
    const touch = event.touches[0]

    try {
      const query = uni.createSelectorQuery().in(instance).select('#guideCanvas')
      query
        .boundingClientRect((data: any) => {
          if (data) {
            const x = touch.pageX - data.left
            const y = touch.pageY - data.top

            state.touchPosition = { x, y }
            checkBeadTouch(x, y)
          }
        })
        .exec()
    } catch (err) {
      console.error('获取坐标时出错:', err)
    }
  }
}

const handleTouchMove = (event) => {
  // 引导模式下简化手势处理
}

const handleTouchEnd = (event) => {
  // 引导模式下简化手势处理
}

// 检查触摸点是否在珠子上
const checkBeadTouch = (touchX, touchY) => {
  if (!ctx || !canvasEl || !state.beadInfoArray) return

  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y

  for (const info of state.beadInfoArray) {
    const distance = Math.sqrt(
      Math.pow(touchX - info.centerX, 2) + Math.pow(touchY - info.centerY, 2),
    )
    const beadSize = info.beadSize * 0.95

    if (distance <= beadSize) {
      // 选中珠子
      state.selectedIndex = info.index
      drawBracelet()

      // 如果是引导模式，检查引导步骤
      if (showGuideMask.value) {
        if (currentGuideStep.value.type === 'bead-hole' && info.index === 0) {
          // 如果点击的是第一个位置，进入下一步
          nextGuideStep()
        }
      }
      break
    }
  }
}

// 选择前一个珠子
const selectPrevBead = () => {
  if (state.selectedIndex === -1) return
  const totalPositions = state.beads.length
  const prevIndex = (state.selectedIndex - 1 + totalPositions) % totalPositions
  state.selectedIndex = prevIndex
  drawBracelet()
}

// 选择后一个珠子
const selectNextBead = () => {
  if (state.selectedIndex === -1) return
  const totalPositions = state.beads.length
  const nextIndex = (state.selectedIndex + 1) % totalPositions
  state.selectedIndex = nextIndex
  drawBracelet()
}

// 清除选中的珠子
const clearSelection = () => {
  if (state.selectedIndex === -1) return

  if (state.beads[state.selectedIndex] && !state.beads[state.selectedIndex].isEmptyHole) {
    state.beads[state.selectedIndex] = null
    recalculateBeadPositions()
    drawBracelet()
  }

  state.selectedIndex = -1
}

// 裁剪珠子
const cropBeads = () => {
  const existingBeads = state.beads.filter((bead) => bead && !bead.isEmptyHole)

  if (existingBeads.length === 0) {
    initDemoData()
    state.selectedIndex = -1
    drawBracelet()
    return
  }

  state.beads = existingBeads
  state.selectedIndex = -1
  recalculateBeadPositions()
  drawBracelet()
}

// 显示珠子详情
const showBeadInfo = (bead) => {
  beadInfo.value = bead
  showPopup.value = true

  // 引导检查：等待弹窗动画完成后再进入下一步
  if (showGuideMask.value && currentGuideStep.value.type === 'bead-selection') {
    setTimeout(() => {
      nextGuideStep()
    }, 300) // 与弹窗动画时长保持一致
  }
}

// 添加到手串
const addToBracket = (beadInfo, sku) => {
  const timestamp = new Date().getTime()
  const bead = {
    id: beadInfo.id,
    name: beadInfo.name,
    image: beadInfo.image,
    imagePath: beadInfo.image + '?t=' + timestamp,
    size: sku.diameter,
    materialName: beadInfo.materialName,
    price: sku.price,
    skuId: sku.id,
    uniqueId: timestamp,
  }

  let positionIndex = state.selectedIndex

  if (positionIndex === -1 || positionIndex >= state.beads.length) {
    positionIndex = state.beads.findIndex((b) => b === null)
    if (positionIndex === -1) {
      positionIndex = state.beads.length
      state.beads.push(null)
    }
  }

  state.beads[positionIndex] = bead
  state.selectedIndex = -1

  recalculateBeadPositions()
  drawBracelet()

  if (showGuideMask.value && currentGuideStep.value.type === 'add-button') {
    // 进入下一步，引导用户点击返回或完成
    nextGuideStep()
  } else {
    closePopup()
  }
}

// 关闭弹窗
const closePopup = () => {
  showPopup.value = false

  if (showGuideMask.value && currentGuideStep.value.type === 'popup-bottom-buttons') {
    nextGuideStep()
  }
}

// 查看珠子详情
const onGemDetail = () => {
  uni.showToast({
    title: '这是演示模式',
    icon: 'none',
  })
}

// 完成制作
const onComplete = () => {
  uni.showToast({
    title: '恭喜完成引导！',
    icon: 'success',
  })

  setTimeout(() => {
    closeGuide()
  }, 1500)

  // 引导检查
  if (showGuideMask.value && currentGuideStep.value.type === 'complete-button') {
    closeGuide()
  }
}

// 组件定义
const emit = defineEmits(['close'])

onMounted(() => {
  console.log('GuideDemo 组件已挂载') // 真机调试入口日志
  // 初始化 canvas，成功后由 initCanvas 回调内部触发 showGuide
  setTimeout(() => {
    initCanvas()
  }, 300)
})

onBeforeUnmount(() => {
  if (state.animationTimer) {
    clearTimeout(state.animationTimer)
  }
  canvasEl = null
  ctx = null
})

// 初始化canvas
const initCanvas = () => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    const screenWidth = systemInfo.windowWidth
    const safeAreaInsets = {
      top: systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.top : 0,
      bottom: systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0,
    }
    const bottomAreaHeight = 220 + safeAreaInsets.bottom
    const availableHeight = systemInfo.windowHeight - bottomAreaHeight - safeAreaInsets.top

    state.transformOrigin = {
      x: screenWidth / 2,
      y: availableHeight / 2,
    }

    // 先尝试新版 2dCanvas
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('#guideCanvas')
      .fields({ node: true, size: true }, (res: any) => {
        if (res && res.node) {
          console.log('使用 2dCanvas node 初始化')
          const canvas = res.node
          canvas.width = screenWidth * systemInfo.pixelRatio
          canvas.height = availableHeight * systemInfo.pixelRatio
          ctx = canvas.getContext('2d')
          ctx.scale(systemInfo.pixelRatio, systemInfo.pixelRatio)
          canvasEl = { canvas, width: screenWidth, height: availableHeight, node: canvas }
          afterCanvasReady()
        } else {
          // 真机或低版本兜底：使用 createCanvasContext
          console.log('fallback 使用 createCanvasContext')
          uni
            .createSelectorQuery()
            .in(instance)
            .select('#guideCanvas')
            .boundingClientRect((rect: any) => {
              if (!rect) {
                console.error('无法获取 canvas 尺寸')
                return
              }
              canvasEl = { width: rect.width, height: rect.height }
              ctx = uni.createCanvasContext('guideCanvas') as any
              // createCanvasContext 不支持 scale 像素比，手动设置 transformOrigin 已处理
              afterCanvasReady()
            })
            .exec()
        }
      })
      .exec()
  } catch (error) {
    console.error('Canvas初始化出错:', error)
  }
}

const afterCanvasReady = () => {
  if (!ctx) {
    console.error('ctx 初始化失败')
    return
  }
  state.scale = 1
  state.rotation = 0
  initDemoData()
  state.canvasReady = true
  recalculateBeadPositions()
  drawBracelet()
  nextTick(() => {
    console.log('Canvas 准备就绪，启动引导')
    showGuide()
  })
}

// 重新计算珠子位置
const recalculateBeadPositions = () => {
  if (!canvasEl || !ctx) return

  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y
  const size = Math.min(canvasEl.width, canvasEl.height)
  const referenceRadius = size * 0.35
  const baseBeadSize = referenceRadius * 0.18
  const beadInfoArray = []

  const actualBeadCount = state.totalBeads

  for (let i = 0; i < actualBeadCount; i++) {
    const currentBead = state.beads[i]
    const beadSize = baseBeadSize
    const angle = (i * 2 * Math.PI) / actualBeadCount - Math.PI / 2
    const x = centerX + referenceRadius * Math.cos(angle)
    const y = centerY + referenceRadius * Math.sin(angle)

    beadInfoArray.push({
      index: i,
      beadSize,
      hasBead: !!(currentBead && !currentBead.isEmptyHole),
      bead: currentBead,
      centerX: x,
      centerY: y,
      centerAngle: angle,
      beadAngle: 0,
    })
  }

  state.beadInfoArray = beadInfoArray
  state.actualRadius = referenceRadius
}

// 绘制手串
const drawBracelet = () => {
  if (!ctx || !canvasEl) return

  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)

  const centerX = state.transformOrigin.x
  const centerY = state.transformOrigin.y

  ctx.save()
  ctx.translate(centerX, centerY + state.translateY)
  ctx.rotate(state.rotation)
  ctx.scale(state.scale, state.scale)
  ctx.translate(-centerX, -centerY)

  drawBeadHoles(centerX, centerY)

  ctx.restore()
}

const imageCache = {}

const drawBeadHoles = (centerX, centerY) => {
  const beadInfoArray = state.beadInfoArray || []

  for (const info of beadInfoArray) {
    const currentBead = state.beads[info.index]
    const x = info.centerX
    const y = info.centerY
    const beadSize = info.beadSize
    const isSelected = info.index === state.selectedIndex

    // 绘制选中效果
    if (isSelected) {
      ctx.beginPath()
      ctx.arc(x, y, beadSize + 5, 0, Math.PI * 2)
      ctx.fillStyle = 'rgba(244, 67, 54, 0.3)'
      ctx.fill()
    }

    if (currentBead && !currentBead.isEmptyHole) {
      // 先绘制珠子底座（白边）
      ctx.beginPath()
      ctx.arc(x, y, beadSize, 0, Math.PI * 2)
      ctx.fillStyle = '#ffffff'
      ctx.fill()

      // 创建或获取缓存图片
      const imgSrc = currentBead.imagePath || currentBead.image
      const cached = imageCache[imgSrc]

      const drawImg = (img) => {
        ctx.save()
        ctx.beginPath()
        ctx.arc(x, y, beadSize * 0.95, 0, Math.PI * 2)
        ctx.clip()
        ctx.drawImage(img, x - beadSize * 0.95, y - beadSize * 0.95, beadSize * 1.9, beadSize * 1.9)
        ctx.restore()
      }

      if (cached && cached.complete) {
        drawImg(cached)
      } else if (!cached) {
        try {
          const img = canvasEl?.node?.createImage ? canvasEl.node.createImage() : null
          if (img) {
            imageCache[imgSrc] = img
            img.onload = () => {
              drawImg(img)
            }
            img.src = imgSrc
          } else {
            ctx.beginPath()
            ctx.arc(x, y, beadSize * 0.95, 0, Math.PI * 2)
            ctx.fillStyle = getBeadColor(currentBead.materialName)
            ctx.fill()
          }
        } catch (e) {
          ctx.beginPath()
          ctx.arc(x, y, beadSize * 0.95, 0, Math.PI * 2)
          ctx.fillStyle = getBeadColor(currentBead.materialName)
          ctx.fill()
        }
      }

      if (isSelected) {
        ctx.beginPath()
        ctx.lineWidth = 2
        ctx.arc(x, y, beadSize, 0, Math.PI * 2)
        ctx.strokeStyle = '#f44336'
        ctx.stroke()
      }
    } else {
      // 绘制空孔位
      ctx.beginPath()
      ctx.arc(x, y, beadSize * 0.95, 0, Math.PI * 2)
      ctx.fillStyle = isSelected ? '#fff3f3' : '#f5f5f5'
      ctx.fill()

      ctx.beginPath()
      ctx.lineWidth = 1
      ctx.arc(x, y, beadSize * 0.95, 0, Math.PI * 2)
      ctx.strokeStyle = isSelected ? '#f44336' : '#dddddd'
      ctx.stroke()
    }
  }
}

// 获取珠子颜色（根据材质）
const getBeadColor = (materialName) => {
  const colorMap = {
    天然玛瑙: '#ff6b6b',
    天然水晶: '#74c0fc',
    足金999: '#ffd43b',
    天然翡翠: '#51cf66',
  }
  return colorMap[materialName] || '#adb5bd'
}

// 引导相关函数
const showGuide = () => {
  console.log('开始显示引导')
  showGuideMask.value = true
  currentStepIndex.value = 0

  // 延迟计算位置，确保DOM已渲染
  nextTick(() => {
    setTimeout(() => {
      calculateTargetPosition()
    }, 100)
  })
}

const closeGuide = () => {
  console.log('关闭引导')
  showGuideMask.value = false
  currentStepIndex.value = 0
  hasShownGuide.value = true

  // 发送关闭事件给父组件
  emit('close')
}

// 打开演示SKU弹窗
const openDemoSkuPopup = () => {
  if (demoBeads.value && demoBeads.value.length) {
    beadInfo.value = demoBeads.value[0]
    showPopup.value = true
  }
}

// 修改 nextGuideStep 逻辑
const nextGuideStep = () => {
  console.log('下一步引导:', currentStepIndex.value)
  if (currentStepIndex.value < guideSteps.length - 1) {
    currentStepIndex.value++

    // 如果进入 add-button 步骤且弹窗未打开，自动弹出演示弹窗
    if (currentGuideStep.value.type === 'add-button' && !showPopup.value) {
      openDemoSkuPopup()
      // 等弹窗动画结束再定位
      setTimeout(() => {
        calculateTargetPosition()
      }, 350)
      return
    }

    nextTick(() => {
      calculateTargetPosition()
    })
  } else {
    closeGuide()
  }
}

// 计算目标位置
const calculateTargetPosition = () => {
  const step = currentGuideStep.value
  if (!step) return

  console.log('计算引导位置:', step.type, 'showGuideMask:', showGuideMask.value)

  if (step.type === 'canvas-area') {
    // 高亮整个手串区域，基于 canvas 绝对坐标
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('#guideCanvas')
      .boundingClientRect((rect: any) => {
        if (rect && !Array.isArray(rect)) {
          const radius = state.actualRadius || 100
          const beadRadius = radius * 0.18 // 与 recalculateBeadPositions 中一致
          const margin = 10 // 额外边距
          const radiusExt = radius + beadRadius + margin
          // 计算 highlight 位置（page 坐标）
          const highlightLeft = rect.left + state.transformOrigin.x - radiusExt
          const highlightTop = rect.top + state.transformOrigin.y - radiusExt
          const highlightSize = radiusExt * 2

          highlightPosition.value = {
            left: highlightLeft,
            top: highlightTop + 10,
            width: highlightSize,
            height: highlightSize,
          }

          // 提示框居中显示在高亮区域下方（使用 translateX(-50%)）
          tooltipPosition.value = {
            left: highlightLeft + highlightSize / 2, // center X
            top: highlightTop + highlightSize + 30, // 下方 30px
            direction: 'top',
          }

          console.log('设置手串高亮:', highlightPosition.value)
          console.log('设置提示框位置:', tooltipPosition.value)
        }
      })
      .exec()
  } else if (step.type === 'bead-hole') {
    // 高亮第一个空位
    const targetBead = state.beadInfoArray.find((info) => info.index === 0)
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('#guideCanvas')
      .boundingClientRect((rect: any) => {
        if (rect && targetBead) {
          const beadSize = targetBead.beadSize
          const margin = 15
          const highlightLeft = rect.left + targetBead.centerX - beadSize - margin
          const highlightTop = rect.top + targetBead.centerY - beadSize - margin
          const highlightSize = (beadSize + margin) * 2

          highlightPosition.value = {
            left: highlightLeft,
            top: highlightTop,
            width: highlightSize,
            height: highlightSize,
          }

          const screenWidth = uni.getSystemInfoSync().windowWidth
          tooltipPosition.value = {
            left: Math.max(20, Math.min(screenWidth - 20, highlightLeft + highlightSize / 2)),
            top: highlightTop + highlightSize + 30,
            direction: 'top',
          }

          console.log('设置珠子孔高亮:', highlightPosition.value)
          console.log('设置提示框位置:', tooltipPosition.value)
        }
      })
      .exec()
  } else if (step.type === 'bead-selection') {
    // 高亮珠子选择区域
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('.tabs-area')
      .boundingClientRect()
      .select('.tabs-area scroll-view') // 列表区域
      .boundingClientRect()
      .exec((rects) => {
        const tabsRect = rects[0]
        const scrollRect = rects[1]
        if (tabsRect) {
          // 高亮仅含两行 Tabs
          const highlightHeight = 80

          highlightPosition.value = {
            left: tabsRect.left - 5,
            top: tabsRect.top - 5,
            width: tabsRect.width + 10,
            height: highlightHeight + 10,
          }

          tooltipPosition.value = {
            left: highlightPosition.value.left + highlightPosition.value.width / 2,
            top: highlightPosition.value.top - 190,
            direction: 'bottom',
          }
        }
      })
  } else if (step.type === 'action-buttons') {
    // 高亮左侧操作按钮及裁剪按钮
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('.action-buttons-left')
      .boundingClientRect((data: any) => {
        console.log('左侧操作按钮容器:', data)
        if (data && !Array.isArray(data)) {
          highlightPosition.value = {
            left: data.left - 5,
            top: data.top - 5,
            width: data.width + 10,
            height: data.height + 10,
          }

          tooltipPosition.value = {
            left: highlightPosition.value.left + highlightPosition.value.width / 2,
            top: highlightPosition.value.top - 240,
            direction: 'bottom',
          }
        }
      })
      .exec()
  } else if (step.type === 'add-button') {
    nextTick(() => {
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(instance)
        query
          .select('#addButtonTopArea')
          .boundingClientRect((rect: any) => {
            console.log('规格列表顶部两行区域:', rect)
            if (rect && !Array.isArray(rect)) {
              highlightPosition.value = {
                left: rect.left - 5,
                top: rect.top - 10,
                width: rect.width + 10,
                height: rect.height + 10,
              }

              tooltipPosition.value = {
                left: highlightPosition.value.left + highlightPosition.value.width / 2,
                top: highlightPosition.value.top - 200,
                direction: 'bottom',
              }

              popupRetryCount = 0
            } else {
              if (popupRetryCount < 5) {
                popupRetryCount++
                setTimeout(() => {
                  calculateTargetPosition()
                }, 120)
              }
            }
          })
          .exec()
      }, 350) // 等待弹窗彻底渲染完成
    })
  } else if (step.type === 'popup-bottom-buttons') {
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('#popupBtns')
      .boundingClientRect((rect: any) => {
        if (rect && !Array.isArray(rect)) {
          highlightPosition.value = {
            left: rect.left - 5,
            top: rect.top - 5,
            width: rect.width + 10,
            height: rect.height + 10,
          }

          tooltipPosition.value = {
            left: highlightPosition.value.left + highlightPosition.value.width / 2,
            top: highlightPosition.value.top - 240,
            direction: 'bottom',
          }
        }
      })
      .exec()
  } else if (step.type === 'popup-bottom-add-buttons') {
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('.size-price-list')
      .boundingClientRect()
      .select('#popupBtns')
      .boundingClientRect()
      .exec((rects) => {
        const listRect = rects[0]
        const btnRect = rects[1]
        if (listRect && btnRect) {
          // 计算每行高度
          const rowHeight = listRect.height / (beadInfo.value?.skuList.length || 1)
          const highlightTop = Math.max(listRect.top, listRect.bottom - rowHeight * 2) - 5
          const highlightBottom = btnRect.bottom + 5

          highlightPosition.value = {
            left: btnRect.left - 5,
            top: highlightTop,
            width: btnRect.width + 10,
            height: highlightBottom - highlightTop,
          }

          tooltipPosition.value = {
            left: highlightPosition.value.left + highlightPosition.value.width / 2,
            top: highlightPosition.value.top - 200,
            direction: 'bottom',
          }
        }
      })
  } else if (step.type === 'bracelet-preview') {
    if (showPopup.value) closePopup()
    ensureBraceletPreviewData()
    const extraMargin = 15
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('#guideCanvas')
      .boundingClientRect((rect: any) => {
        if (rect && !Array.isArray(rect)) {
          const radius = state.actualRadius || 100
          const beadRadius = radius * 0.18
          const margin = 10
          const radiusExt = radius + beadRadius + margin

          const highlightLeft = rect.left
          const highlightTop = rect.top
          const highlightWidth = rect.width
          const braceletBottom = state.transformOrigin.y + radiusExt
          const highlightHeight = braceletBottom - rect.top + extraMargin

          highlightPosition.value = {
            left: highlightLeft,
            top: highlightTop,
            width: highlightWidth,
            height: highlightHeight,
          }

          tooltipPosition.value = {
            left: highlightLeft + highlightWidth / 2,
            top: highlightTop + highlightHeight + 30,
            direction: 'top',
          }
        }
      })
      .exec()
  } else if (step.type === 'complete-button') {
    // 高亮完成按钮
    const query = uni.createSelectorQuery().in(instance)
    query
      .select('#completeBtnWrapper')
      .boundingClientRect((data: any) => {
        console.log('完成按钮区域数据:', data)
        if (data && !Array.isArray(data) && data.width && data.height) {
          // 直接高亮完成按钮外围
          highlightPosition.value = {
            left: data.left - 5,
            top: data.top - 5,
            width: data.width + 10,
            height: data.height + 10,
          }

          tooltipPosition.value = {
            left: highlightPosition.value.left + highlightPosition.value.width,
            top: highlightPosition.value.top - 180,
            direction: 'bottom-right',
          }

          completeRetryCount = 0 // 重置重试计数器
        } else {
          if (completeRetryCount < 5) {
            completeRetryCount++
            setTimeout(() => {
              calculateTargetPosition()
            }, 120)
          }
        }
      })
      .exec()
  }
}

// 在 <script setup> 顶部添加数据
const mainTab = ref(0)
const subTab = ref(0)
const fiveElements = ['火', '水', '木', '土', '金']

// 预览信息相关
const beadList = computed(() => state.beads.filter((b) => b && !b.isEmptyHole))
const previewSummary = computed(() => {
  const count = beadList.value.length
  if (count === 0) return ''
  // 简易周长估算：直径(mm) * 0.1 得到 cm，再乘数量
  const avgDiameter = beadList.value.reduce((s, b) => s + (b.size || 0), 0) / count
  const lengthCm = ((avgDiameter * count) / 10).toFixed(1)
  const totalPrice = beadList.value.reduce((s, b) => s + parseFloat(b.price || '0'), 0).toFixed(2)
  return `${count}颗 周长${lengthCm}mm 推荐女性佩戴 ¥${totalPrice}`
})
const lastBeadDetail = computed(() => {
  if (beadList.value.length === 0) return ''
  const last = beadList.value[beadList.value.length - 1]
  return `${last.name} ${last.size}mm ¥${last.price}`
})

// 确保第 6 步预览中至少展示两颗演示珠子
const ensureBraceletPreviewData = () => {
  const currentCount = beadList.value.length
  if (currentCount >= 2) return
  const need = 2 - currentCount
  let added = 0
  for (const beadInfoDemo of demoBeads.value) {
    if (added >= need) break
    const sku = beadInfoDemo.skuList[0]
    const timestamp = Date.now() + added
    const bead = {
      id: beadInfoDemo.id,
      name: beadInfoDemo.name,
      image: beadInfoDemo.image,
      imagePath: beadInfoDemo.image + '?t=' + timestamp,
      size: sku.diameter,
      materialName: beadInfoDemo.materialName,
      price: sku.price,
      skuId: sku.id,
      uniqueId: timestamp,
    }
    const idx = state.beads.findIndex((b) => b === null)
    if (idx !== -1) {
      state.beads[idx] = bead
    } else {
      state.beads.push(bead)
    }
    added++
  }
  recalculateBeadPositions()
  drawBracelet()
}

// 重试计数器：等待 SKU 弹窗渲染
let popupRetryCount = 0

// 完成按钮重试计数器
let completeRetryCount = 0
</script>

<style lang="scss" scoped>
.guide-demo-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100vh;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.canvas-container {
  position: relative;
  z-index: 1;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 60vh;
  max-height: 60vh;
  overflow: hidden;
  background: white;
}

.bracelet-canvas {
  position: relative;
  z-index: 2;
  display: block;
  width: 100% !important;
  height: 100% !important;
  touch-action: none;
  background: transparent;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to right, #e0e0e0 1rpx, transparent 1rpx),
    linear-gradient(to bottom, #e0e0e0 1rpx, transparent 1rpx);
  background-size: 20rpx 20rpx;
  opacity: 0.3;
}

/* 底部容器 */
.bottom-container {
  position: relative;
  z-index: 5;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 40vh;
  max-height: 40vh;
  padding: 0 20rpx 20rpx 20rpx;
  overflow-y: auto;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 操作区域 */
.action-area {
  padding: 8rpx;
  padding-bottom: 10rpx;
}

.action-buttons-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.action-buttons {
  display: flex;
  gap: 10rpx;
}

/* 引导遮罩和提示样式 */
.guide-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100004; /* 确保引导层级高于 wd-popup */
  width: 100%;
  height: 100%;
  pointer-events: auto;
}

.highlight-cutout {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  clip-path: polygon(
    0% 0%,
    0% 100%,
    var(--highlight-left) 100%,
    var(--highlight-left) var(--highlight-top),
    calc(var(--highlight-left) + var(--highlight-width)) var(--highlight-top),
    calc(var(--highlight-left) + var(--highlight-width))
      calc(var(--highlight-top) + var(--highlight-height)),
    var(--highlight-left) calc(var(--highlight-top) + var(--highlight-height)),
    var(--highlight-left) 100%,
    100% 100%,
    100% 0%
  );
  pointer-events: none;
}

.highlight-border {
  position: absolute;
  border: 3rpx solid #ff6b6b;
  border-radius: 12rpx;
  pointer-events: none;
  box-shadow:
    0 0 0 2rpx rgba(255, 255, 255, 0.8),
    0 0 20rpx rgba(255, 107, 107, 0.6);
  animation: pulse-border 2s infinite;
  z-index: 100000;
}

@keyframes pulse-border {
  0% {
    box-shadow:
      0 0 0 2rpx rgba(255, 255, 255, 0.8),
      0 0 20rpx rgba(255, 107, 107, 0.6),
      0 0 0 0 rgba(255, 107, 107, 0.4);
  }
  50% {
    box-shadow:
      0 0 0 2rpx rgba(255, 255, 255, 0.8),
      0 0 20rpx rgba(255, 107, 107, 0.8),
      0 0 0 15rpx rgba(255, 107, 107, 0.2);
  }
  100% {
    box-shadow:
      0 0 0 2rpx rgba(255, 255, 255, 0.8),
      0 0 20rpx rgba(255, 107, 107, 0.6),
      0 0 0 0 rgba(255, 107, 107, 0.4);
  }
}

.guide-tooltip {
  position: absolute;
  max-width: 600rpx;
  min-width: 400rpx;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  z-index: 100003;
  transform: translateX(-50%);
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
}

.tooltip-arrow.top {
  top: -20rpx;
  left: 50%;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 20rpx solid #ffffff;
  transform: translateX(-50%);
}

.tooltip-arrow.bottom {
  bottom: -20rpx;
  left: 50%;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-top: 20rpx solid #ffffff;
  transform: translateX(-50%);
}

.tooltip-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.tooltip-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.2s ease;
}

.tooltip-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

/* 珠子详情弹窗样式 */
.bead-info-section {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10rpx;
  margin-top: 35rpx;
  margin-bottom: 45rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
}

.bead-avatar-container {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  padding: 25rpx;
  margin-right: 30rpx;
  overflow: hidden;
  background-color: #fff;
  border: 8rpx solid transparent;
  border-radius: 12rpx;
  background-image: url('/static/about/avtar.svg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-clip: border-box;
}

.bead-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4rpx;
}

.bead-details {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 30rpx;
  justify-content: center;
}

.bead-name {
  margin-bottom: 0;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.bead-material {
  font-size: 27rpx;
  color: #888;
}

.size-price-list {
  display: flex;
  flex-direction: column;
  //   gap: 50rpx;
  padding-left: 40rpx;
}

.size-price-item {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 96rpx;
  padding: 0 24rpx;
  margin-bottom: 0;
  background-color: #ffffff;
  border: 2rpx solid transparent;
  border-radius: 20rpx;
  box-shadow: none;
  transition: all 0.2s;
}
/* 三列等效于 wd-col span 分布：10/24 12/24 2/24 */
.size-price-item .col-size {
  flex: 0 0 41.67%;
}

.size-price-item .col-price {
  flex: 0 0 50%;
}

.size-price-item .col-icon {
  flex: 0 0 8.33%;
  text-align: right;
}

.size-name {
  font-size: 28rpx;
  font-weight: bold;
}

.price {
  font-size: 26rpx;
  color: #555;
}

/* 预览信息样式 */
.preview-info {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-tag {
  display: inline-block;
  padding: 10rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  background-color: #ffffff;
  color: #333;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.guide-tooltip.bottom-right {
  /* 让提示框右侧对齐定位点 */
  transform: translateX(-100%);
}

.tooltip-arrow.bottom-right {
  position: absolute;
  bottom: -20rpx;
  right: 40rpx; /* 与箭头宽度保持一致，可按需调整 */
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-top: 20rpx solid #ffffff;
}
</style>
