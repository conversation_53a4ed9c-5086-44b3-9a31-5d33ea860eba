import { http } from '@/utils/http'
// 轮播图返回类型
export interface IUserInfoItem {
  address?: string
  avatar?: string
  birthArea?: string
  birthTime?: string
  birthday?: string
  color?: string
  enjoyUse?: string
  gender?: string
  handSize?: string
  nickName?: string
  phone?: string
  birthAreaArray?: string
  addressArray?: string
  addressDetail?: string
}
// 色系返回类型
export interface IGemTypeListItem {
  id: string
  typeName: string
  ordered: string
}

export interface IUserInfoQuery {
  address?: string
  avatar?: string
  birthArea?: string
  birthTime?: any
  birthday?: any
  birthdayDisplay?: string
  color?: any
  enjoyUse?: string
  gender?: string
  genderName?: string
  handSize?: string
  handSizeDisplay?: string
  nickName?: string
  phone?: string
  birthAreaArray?: any
  addressArray?: any
  addressDetail?: string
  colorDisplay?: string
}

/** GET 请求 获取用户详情 */
export const userInfo = () => {
  return http.get<IUserInfoItem>('/user/info')
}

/** GET 请求 获取色系字典 */
export const gemTypeList = (type: string) => {
  return http.get<IGemTypeListItem[]>(`/gem/${type}/list`)
}

/** GET 请求 更新用户信息 */
export const userUpdate = (data: IUserInfoQuery) => {
  return http.post<IUserInfoItem>('/user/update', { ...data })
}
