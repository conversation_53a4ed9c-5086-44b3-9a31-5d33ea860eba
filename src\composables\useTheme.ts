import { ref } from 'vue'
import { useUserStore } from '@/store'

const userStore = useUserStore()

// 控制登录提示弹窗是否显示
const showLoginModal = ref(false)

export function useLoginGuard() {
  /**
   * @param requireLogin 是否需要登录才继续
   */
  function checkLogin(requireLogin = true): boolean {
    if (requireLogin && !userStore.isLogined) {
      showLoginModal.value = true
      return false
    }

    showLoginModal.value = false
    return true
  }

  return {
    showLoginModal,
    checkLogin,
  }
}
