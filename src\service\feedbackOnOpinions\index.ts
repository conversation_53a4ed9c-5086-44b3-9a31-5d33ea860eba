import { http } from '@/utils/http'

// 反馈类别参数类型
export interface ITypeListItem {
  id: string
  name: string
  ordered: number
}

// 添加反馈类型参数
export interface IFeedbackAddQuery {
  feedbackTypeId: string
  picture: string[]
  content: string
}

// 添加反馈列表类型参数
export interface IFeedbackPageQuery {
  sort?: Array<string>
  page?: number
  size?: number
}

// 添加反馈列表返回类型参数
export interface IFeedbackItem {
  id: number
  feedbackTypeId: number
  feedbackTypeName: string
  pictures: string[]
  content: string
  answerContent: string
  answerTime: string
  status: number
  createTime: string
}

export interface IFeedbackListResponse {
  list: IFeedbackItem[]
  total: number
}

interface GemstoneRecommendation {
  name: string
  reason: string
}

interface GemItem {
  id: string
  name: string
}

interface LuckyItem {
  id: string
  name: string
  luckyTypeId: string
  ordered: number
  content: string
  gemList: GemItem[]
  labels: string[]
  picture: string
  price: number
  url: string
}

export interface IAiChatItem {
  id: string
  fiveElement: string
  enjoyUse: string
  gemstoneRecommendations: GemstoneRecommendation[]
  healthLuck: string
  list: LuckyItem[]
  moneyLuck: string
  overallFortune: string
  romanticLuck: string
  todayFortune: string
}

export interface IAiChatQuery {
  birthDay?: number | string
  birthTime?: number | string
  birthArea?: string
  gender?: number | string
  isSelf?: number
}

export let testResult: IAiChatItem = null

export function getTestResult(obj: IAiChatItem) {
  testResult = obj
}

/** GET 请求 查询反馈类别 */
export const feedbackTypeList = () => {
  return http.get<ITypeListItem[]>('/feedback/type/list')
}

/** POST 请求 */
export const feedbackAdd = (data: IFeedbackAddQuery) => {
  return http.post<boolean>('/feedback/add', { ...data })
}

/** POST 请求 */
export const feedbackPage = (data: IFeedbackPageQuery) => {
  return http.get<IFeedbackListResponse>('/feedback/page', { ...data })
}

/** GET 请求 开始测试八字 */
export const aiChat = (data: IAiChatQuery) => {
  return http.post<IAiChatItem>('/ai/chat', { ...data }, {}, { timeout: 35 * 1000 })
}

/** GET 请求 开始测试八字 */
export const getAiChat = (id: string) => {
  return http.get<IAiChatItem>(`/ai/chat${id}`)
}

/** GET 请求 获取分享测试结果 */
export const shareDetail = (id: string) => {
  return http.get<IAiChatItem>(`/ai/shareDetail/${id}`)
}

/** GET 请求 撤销反馈 */
export const feedbackDelete = (id: string) => {
  return http.get<boolean>(`/feedback/delete/${id}`)
}
