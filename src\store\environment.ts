import { defineStore } from 'pinia'
import { ref } from 'vue'

const initState = { isEnvironment: '' }

export const useEnvironmentStore = defineStore(
  'environment',
  () => {
    const environmentInfo = ref<IEnvironmentInfo>({ ...initState })

    const setEnvironmentInfo = (val: IEnvironmentInfo) => {
      environmentInfo.value = { ...environmentInfo.value, ...val }
    }

    const clearEnvironmentInfo = () => {
      environmentInfo.value = { ...initState }
    }
    // 一般没有reset需求，不需要的可以删除
    const reset = () => {
      environmentInfo.value = { ...initState }
    }

    return {
      environmentInfo,
      setEnvironmentInfo,
      clearEnvironmentInfo,
      reset,
    }
  },
  {
    persist: true,
  },
)
